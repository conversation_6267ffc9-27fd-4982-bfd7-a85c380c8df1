# NtfyClip for macOS

NtfyClip for macOS is a native macOS menu bar utility designed to seamlessly connect your clipboard with the ntfy service. It monitors clipboard changes in real-time and syncs them to a specified ntfy topic. It can also subscribe to an ntfy topic and automatically update the local clipboard with received messages.

## Features

- 🔄 **Bidirectional Sync**: Supports two-way data flow from clipboard to ntfy (send) and ntfy to clipboard (receive).
- 📝 **Multi-Content Support**: Reliably syncs plain text and image content.
- 🎨 **Modern UI**: A sleek and intuitive interface built with SwiftUI, following the latest macOS design guidelines.
-  Sidebar navigation for easy access to Status, Settings, and Logs.
- 🎯 **Menu Bar Integration**: A lightweight menu bar app that doesn’t clutter your Dock.
- 🔒 **Loop Protection**: Built-in intelligent loop protection to prevent infinite sync loops.
- 🔐 **Authentication Support**: Supports username/password authentication for ntfy servers.
- ⚡ **High Performance**: Built with the Swift Actor model to ensure concurrency safety.
- 🛡️ **Error Handling**: Graceful error handling and automatic reconnection.

## System Requirements

- macOS 13.0 (Ventura) or later
- Xcode 14.0+ or Swift 5.7+ command-line tools

## Installation and Running

### Using the Makefile (Recommended)

1.  **Clone the project**
    ```bash
    git clone https://github.com/your-username/NtfyClip.git
    cd NtfyClip
    ```

2.  **Build the project**
    ```bash
    make build
    ```
    This command uses `xcodebuild` to create a proper application bundle in the `.build` directory.

3.  **Run the application**
    ```bash
    make run
    ```
    This will launch the `NtfyClip.app` bundle.

4.  **Install the application**
    ```bash
    make install
    ```
    This will build the application and copy it to your `/Applications` folder.

### Using Xcode

1.  Open the `NtfyClip.xcodeproj` file in Xcode.
2.  Wait for the dependencies to resolve.
3.  Select the `NtfyClip` scheme.
4.  Click the Run button (⌘+R).

## How to Use

1.  **Launch the app**. You will see a clipboard icon in your menu bar and the main window will appear.
2.  **Navigate to the Settings tab** in the main window.
3.  **Configure your ntfy server(s)**. You can set up separate configurations for sending and receiving.
    -   **Ntfy Server URL**: The address of your ntfy server (e.g., `https://ntfy.sh`).
    -   **Topic Name**: The topic to sync with.
    -   **Authentication** (Optional): Enable and provide credentials if your server requires it.
4.  **Save your configuration**.
5.  **Navigate to the Status tab** and click "Start Sync".
6.  The app will now sync your clipboard with the configured ntfy topic(s).

## Architecture

The project follows a modern, three-layer architecture:

### View Layer
- `MainWindowView`: The main application window with a `NavigationSplitView`.
- `StatusDetailView`: Displays the current status of the application.
- `SettingsDetailView`: Provides the user interface for configuring the application.
- `LogsDetailView`: Displays logs for debugging.
- `MenuBarView`: The view for the menu bar extra.

### ViewModel Layer
- `AppViewModel`: The central state manager for the application.

### Services & Actors Layer
- `ClipboardSender` & `ClipboardReceiver`: Actors that handle the core logic of sending and receiving clipboard data.
- `NtfyService`: Manages communication with the ntfy server.
- `ClipboardManager`: Handles interactions with the system clipboard.
- `ConfigurationManager`: Manages the application's settings.
- `LogManager`: Manages logging.

## Contributing

Issues and Pull Requests are welcome!

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
