import Foundation
import OSLog
import AppKit

actor ClipboardSender {
    // MARK: - Properties
    private weak var appViewModel: AppViewModel?
    private let ntfyService: NtfyService
    private let clipboardManager: ClipboardManager
    private let configurationManager: ConfigurationManager
    private let logger = Logger(subsystem: "com.ntfyclip.app", category: "ClipboardSender")
    
    private var isMonitoring = false
    private var monitoringTask: Task<Void, Never>?
    private var lastChangeCount: Int = 0
    
    // MARK: - Configuration
    private let monitoringInterval: TimeInterval = 0.5 // Check every 500ms
    private let maxRetryAttempts = 3
    private let retryDelay: TimeInterval = 2.0
    
    // MARK: - Initialization
    init(appViewModel: AppViewModel, 
         ntfyService: NtfyService, 
         clipboardManager: ClipboardManager, 
         configurationManager: ConfigurationManager) {
        self.appViewModel = appViewModel
        self.ntfyService = ntfyService
        self.clipboardManager = clipboardManager
        self.configurationManager = configurationManager
        
        logger.info("ClipboardSender initialized")
    }
    
    // MARK: - Public Methods
    func startMonitoring() async {
        guard !isMonitoring else {
            logger.warning("Already monitoring clipboard")
            return
        }
        
        isMonitoring = true
        
        // Initialize with current change count
        lastChangeCount = await MainActor.run {
            clipboardManager.getChangeCount()
        }
        
        logger.info("Starting clipboard monitoring")
        
        monitoringTask = Task {
            await monitorClipboard()
        }
    }
    
    func stopMonitoring() async {
        guard isMonitoring else { return }
        
        isMonitoring = false
        monitoringTask?.cancel()
        monitoringTask = nil
        
        logger.info("Stopped clipboard monitoring")
    }
    
    // MARK: - Private Methods
    private func monitorClipboard() async {
        while isMonitoring && !Task.isCancelled {
            do {
                await checkForClipboardChanges()
                
                // Wait for the next check
                try await Task.sleep(nanoseconds: UInt64(monitoringInterval * 1_000_000_000))
                
            } catch is CancellationError {
                logger.info("Clipboard monitoring cancelled")
                break
            } catch {
                logger.error("Error in clipboard monitoring: \(error.localizedDescription)")
                
                // Wait before retrying
                try? await Task.sleep(nanoseconds: UInt64(retryDelay * 1_000_000_000))
            }
        }
    }
    
    private func checkForClipboardChanges() async {
        let currentChangeCount = await MainActor.run {
            clipboardManager.getChangeCount()
        }
        
        guard currentChangeCount != lastChangeCount else {
            return // No change
        }
        
        lastChangeCount = currentChangeCount
        logger.debug("Clipboard change detected (count: \(currentChangeCount))")
        
        // Get current clipboard content
        guard let content = await MainActor.run(body: {
            clipboardManager.getCurrentContent()
        }) else {
            logger.debug("No valid content in clipboard")
            return
        }
        
        // Generate content identifier
        let contentId = content.identifier
        
        // Check with AppViewModel if we should send this content
        let shouldSend = await checkShouldSendContent(identifier: contentId)
        
        guard shouldSend else {
            logger.debug("Skipping send for content ID: \(contentId) (loop prevention)")
            return
        }
        
        // Send the content
        await sendContent(content)
    }
    
    private func sendContent(_ content: ClipboardContent) async {
        guard configurationManager.enableSending,
              configurationManager.isSendConfigurationValid() else {
            logger.error("Invalid send configuration, cannot send content")
            await notifyError(CriticalError.invalidConfiguration)
            return
        }
        
        var attempt = 0
        while attempt < maxRetryAttempts {
            do {
                try await ntfyService.sendClipboardContent(content, using: configurationManager)
                
                logger.info("Successfully sent clipboard content (attempt \(attempt + 1))")
                
                // Notify success
                await notifySuccessfulSync()
                
                return
                
            } catch {
                attempt += 1
                logger.warning("Failed to send content (attempt \(attempt)): \(error.localizedDescription)")
                
                if attempt >= maxRetryAttempts {
                    logger.error("Max retry attempts reached, giving up")
                    await notifyError(error)
                    return
                }
                
                // Wait before retrying
                let delay = retryDelay * Double(attempt) // Exponential backoff
                try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            }
        }
    }
    
    private func notifyError(_ error: Error) async {
        if let viewModel = appViewModel {
            await MainActor.run {
                viewModel.handleError(error)
            }
        }
    }

    private func checkShouldSendContent(identifier: String) async -> Bool {
        if let viewModel = appViewModel {
            return await MainActor.run {
                viewModel.shouldSendContent(identifier: identifier)
            }
        }
        return false
    }

    private func notifySuccessfulSync() async {
        if let viewModel = appViewModel {
            await MainActor.run {
                viewModel.lastSyncTime = Date()
            }
        }
    }
}

// MARK: - ClipboardSender Extensions

extension ClipboardSender {
    /// Get current monitoring status
    var isCurrentlyMonitoring: Bool {
        return isMonitoring
    }
    
    /// Get last processed change count
    var currentChangeCount: Int {
        return lastChangeCount
    }
}
