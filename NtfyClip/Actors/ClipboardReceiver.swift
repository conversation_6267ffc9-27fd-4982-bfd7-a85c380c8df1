import Foundation
import OSLog
import AppKit

actor ClipboardR<PERSON>ei<PERSON> {
    // MARK: - Properties
    private weak var appViewModel: AppViewModel?
    private let ntfyService: NtfyService
    private let clipboardManager: ClipboardManager
    private let configurationManager: ConfigurationManager
    private let logger = Logger(subsystem: "com.ntfyclip.app", category: "ClipboardReceiver")
    
    private var isReceiving = false
    private var receivingTask: Task<Void, Never>?
    private var lastMessageId: String?
    
    // MARK: - Configuration
    private let pollingInterval: TimeInterval = 5.0 // Poll every 5 seconds
    private let maxRetryAttempts = 3
    private let retryDelay: TimeInterval = 10.0
    
    // MARK: - Initialization
    init(appViewModel: AppViewModel, 
         ntfyService: NtfyService, 
         clipboardManager: ClipboardManager, 
         configurationManager: ConfigurationManager) {
        self.appViewModel = appViewModel
        self.ntfyService = ntfyService
        self.clipboardManager = clipboardManager
        self.configurationManager = configurationManager
        
        logger.info("ClipboardReceiver initialized")
    }
    
    // MARK: - Public Methods
    func startReceiving() async {
        guard !isReceiving else {
            logger.warning("Already receiving messages")
            return
        }

        // Don't set lastMessageId initially - let it be nil to get all messages first time
        if lastMessageId == nil {
            logger.info("Starting fresh - will get recent messages first time")
        }

        isReceiving = true
        logger.info("Starting message receiving")

        receivingTask = Task {
            await receiveMessages()
        }
    }
    
    func stopReceiving() async {
        guard isReceiving else { return }
        
        isReceiving = false
        receivingTask?.cancel()
        receivingTask = nil
        
        logger.info("Stopped message receiving")
    }
    
    // MARK: - Private Methods
    private func receiveMessages() async {
        var consecutiveErrors = 0
        
        while isReceiving && !Task.isCancelled {
            do {
                try await pollForMessages()
                consecutiveErrors = 0 // Reset error count on success
                
                // Wait for the next poll
                try await Task.sleep(nanoseconds: UInt64(pollingInterval * 1_000_000_000))
                
            } catch is CancellationError {
                logger.info("Message receiving cancelled")
                break
            } catch {
                consecutiveErrors += 1
                logger.error("Error receiving messages (attempt \(consecutiveErrors)): \(error.localizedDescription)")
                
                if consecutiveErrors >= maxRetryAttempts {
                    logger.error("Max consecutive errors reached, notifying error")
                    await notifyError(error)
                    consecutiveErrors = 0 // Reset to continue trying
                }
                
                // Wait before retrying with exponential backoff
                let delay = retryDelay * Double(consecutiveErrors)
                try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            }
        }
    }
    
    private func pollForMessages() async throws {
        guard configurationManager.enableReceiving,
              configurationManager.isReceiveConfigurationValid() else {
            throw CriticalError.invalidConfiguration
        }

        // Use lastMessageId as 'since' parameter to get only new messages
        // If lastMessageId is nil, don't use since parameter to get only the latest message
        let messages = try await ntfyService.pollMessages(
            using: configurationManager,
            since: lastMessageId
        )
        
        logger.debug("Polled \(messages.count) messages")
        
        // Process each message
        for message in messages {
            await processMessage(message)
            lastMessageId = message.id // Update last processed message ID
        }
    }
    
    private func processMessage(_ message: NtfyMessage) async {
        guard message.isMessage else {
            logger.debug("Skipping non-message event: \(message.event)")
            return
        }
        
        logger.info("Processing message: \(message.id)")
        
        // Handle text message
        if let messageText = message.message, !messageText.isEmpty {
            await processTextMessage(messageText)
            return
        }
        
        // Handle attachment
        if let attachment = message.attachment {
            await processAttachment(attachment)
            return
        }
        
        logger.debug("Message has no processable content")
    }
    
    private func processTextMessage(_ text: String) async {
        let content = ClipboardContent(type: .text, data: text)
        let contentId = content.identifier

        // Notify AppViewModel that we received content
        await notifyContentReceived(identifier: contentId)

        // Update clipboard
        await MainActor.run {
            clipboardManager.setContent(content)
        }

        logger.info("Updated clipboard with received text")
    }
    
    private func processAttachment(_ attachment: NtfyAttachment) async {
        // For now, we'll only handle image attachments
        guard let attachmentType = attachment.type, attachmentType.hasPrefix("image/") else {
            logger.debug("Skipping non-image attachment: \(attachment.type ?? "unknown")")
            return
        }

        do {
            // Download the attachment
            guard let attachmentURLString = attachment.url,
                  let attachmentURL = URL(string: attachmentURLString) else {
                logger.error("Invalid attachment URL: \(attachment.url ?? "nil")")
                return
            }
            
            let (data, _) = try await URLSession.shared.data(from: attachmentURL)
            
            // Create clipboard content
            let content = ClipboardContent(type: .image, data: data)
            let contentId = content.identifier
            
            // Notify AppViewModel that we received content
            await notifyContentReceived(identifier: contentId)
            
            // Update clipboard with image data
            await MainActor.run {
                clipboardManager.setContentFromData(data, type: .image)
            }
            
            logger.info("Updated clipboard with received image")
            
        } catch {
            logger.error("Failed to download attachment: \(error.localizedDescription)")
        }
    }
    
    private func notifyError(_ error: Error) async {
        if let viewModel = appViewModel {
            await MainActor.run {
                viewModel.handleError(error)
            }
        }
    }

    private func notifyContentReceived(identifier: String) async {
        if let viewModel = appViewModel {
            await MainActor.run {
                viewModel.didReceiveContent(identifier: identifier)
            }
        }
    }
}

// MARK: - ClipboardReceiver Extensions

extension ClipboardReceiver {
    /// Get current receiving status
    var isCurrentlyReceiving: Bool {
        return isReceiving
    }
    
    /// Get last processed message ID
    var lastProcessedMessageId: String? {
        return lastMessageId
    }
    
    /// Reset the last message ID (useful for testing or manual reset)
    func resetLastMessageId() async {
        lastMessageId = nil
        logger.info("Reset last message ID")
    }
}
