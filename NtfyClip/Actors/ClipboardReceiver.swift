import Foundation
import OSLog
import AppKit

actor ClipboardReceiver {
    // MARK: - Properties (matching Python receiver.py)
    private weak var appViewModel: AppViewModel?
    private let ntfyService: NtfyService
    private let clipboardManager: ClipboardManager
    private let configurationManager: ConfigurationManager
    private let logger = Logger(subsystem: "com.ntfyclip.app", category: "ClipboardReceiver")

    // MARK: - State Management (matching Python implementation)
    private var isReceiving = false
    private var receivingTask: Task<Void, Never>?
    private var webSocketTask: URLSessionWebSocketTask?

    // MARK: - Configuration (matching Python config)
    private let reconnectDelay: TimeInterval = 5.0 // Reconnect delay in seconds (matching Python)
    private let requestTimeout: TimeInterval = 15.0 // Request timeout (matching Python)
    private let isMacOSImageSupport: Bool = true // Image support enabled (matching Python)

    // MARK: - Error Types
    enum CriticalError: Error {
        case invalidConfiguration
        case webSocketConnectionFailed
    }

    // MARK: - Initialization
    init(appViewModel: AppViewModel,
         ntfyService: NtfyService,
         clipboardManager: ClipboardManager,
         configurationManager: ConfigurationManager) {
        self.appViewModel = appViewModel
        self.ntfyService = ntfyService
        self.clipboardManager = clipboardManager
        self.configurationManager = configurationManager

        logger.info("ClipboardReceiver initialized")
        if isMacOSImageSupport {
            logger.info("macOS image support is enabled")
        }
    }

    // MARK: - Public Methods (matching Python run method)
    func startReceiving() async {
        guard !isReceiving else {
            logger.warning("Already receiving messages")
            return
        }

        guard configurationManager.enableReceiving else {
            logger.info("Ntfy Receiver is disabled in configuration")
            return
        }

        guard configurationManager.isReceiveConfigurationValid() else {
            logger.error("Receiver is enabled but configuration is invalid. Disabling receiver.")
            return
        }

        isReceiving = true
        logger.info("Starting WebSocket listening loop with reconnection logic")

        receivingTask = Task {
            await runWebSocketLoop()
        }
    }

    func stopReceiving() async {
        guard isReceiving else { return }

        isReceiving = false

        // Close WebSocket connection
        webSocketTask?.cancel()
        webSocketTask = nil

        receivingTask?.cancel()
        receivingTask = nil

        logger.info("Stopped message receiving")
    }

    // MARK: - Private Methods (matching Python implementation)

    /// Main WebSocket loop with reconnection logic (matching Python run method)
    private func runWebSocketLoop() async {
        while isReceiving && !Task.isCancelled {
            do {
                logger.info("Attempting to connect to WebSocket")

                // Get topic URL for WebSocket connection
                guard let topicURL = configurationManager.getReceiveTopicURL() else {
                    logger.critical("Invalid WebSocket URL. Receiver stopping.")
                    isReceiving = false
                    break
                }

                let authHeader = configurationManager.getReceiveAuthenticationHeader()

                // Connect to WebSocket
                webSocketTask = try await ntfyService.connectWebSocket(to: topicURL, authHeader: authHeader)

                logger.info("Successfully connected to ntfy topic via WebSocket")

                // Handle messages
                await handleWebSocketMessages()

            } catch is CancellationError {
                logger.info("Receiver task cancelled during shutdown")
                isReceiving = false
                break
            } catch {
                logger.error("WebSocket connection error: \(error.localizedDescription). Retrying in \(self.reconnectDelay)s...")
            }

            // Clean up WebSocket
            webSocketTask?.cancel()
            webSocketTask = nil

            // Wait before reconnecting, only if still enabled and not cancelled
            if isReceiving && !Task.isCancelled {
                do {
                    try await Task.sleep(nanoseconds: UInt64(self.reconnectDelay * 1_000_000_000))
                } catch is CancellationError {
                    logger.info("Receiver reconnect sleep interrupted by cancellation")
                    isReceiving = false
                    break
                } catch {
                    // Handle other sleep errors
                    logger.warning("Sleep interrupted: \(error.localizedDescription)")
                    break
                }
            }
        }

        logger.info("Ntfy Receiver run loop finished")
    }
    
    /// Handle incoming WebSocket messages (matching Python handle_messages)
    private func handleWebSocketMessages() async {
        guard let webSocket = webSocketTask else { return }

        do {
            while isReceiving && !Task.isCancelled {
                let message = try await webSocket.receive()

                switch message {
                case .string(let text):
                    await processWebSocketMessage(text)
                case .data(let data):
                    if let text = String(data: data, encoding: .utf8) {
                        await processWebSocketMessage(text)
                    } else {
                        logger.warning("Received non-UTF8 data message")
                    }
                @unknown default:
                    logger.warning("Received unknown message type")
                }
            }
        } catch is CancellationError {
            logger.info("Receiver message handling loop cancelled")
        } catch {
            logger.warning("WebSocket connection closed while handling messages: \(error.localizedDescription)")
        }
    }

    /// Process individual WebSocket message (matching Python process_ntfy_message)
    private func processWebSocketMessage(_ messageText: String) async {
        do {
            guard let messageData = messageText.data(using: .utf8) else {
                logger.warning("Failed to convert message to data")
                return
            }

            let jsonObject = try JSONSerialization.jsonObject(with: messageData) as? [String: Any]
            guard let json = jsonObject else {
                logger.warning("Failed to parse JSON message")
                return
            }

            let event = json["event"] as? String ?? ""

            switch event {
            case "message":
                await processNtfyMessage(json)
            case "keepalive":
                logger.debug("Received keepalive")
            case "open":
                logger.info("WebSocket connection confirmed open by ntfy")
            case "poll_request":
                logger.debug("Received poll_request signal")
            default:
                logger.warning("Received unknown event type: \(event)")
            }

        } catch {
            logger.warning("Failed to decode JSON message: \(messageText.prefix(200))...")
        }
    }

    /// Process ntfy message event (matching Python process_ntfy_message)
    private func processNtfyMessage(_ data: [String: Any]) async {
        let messageId = data["id"] as? String ?? "N/A"
        let messageContent = data["message"] as? String ?? ""
        let attachment = data["attachment"] as? [String: Any]
        let title = data["title"] as? String ?? ""

        logger.info("Received message (ID: \(messageId), Title: '\(String(title.prefix(30)))...')")

        var textToCopy: String?
        var imageToCopy: Data?
        var imageFilename: String?
        var copySourceDescription = "Unknown"

        // Prioritize Attachment (matching Python implementation)
        if let attachment = attachment {
            let attachUrl = attachment["url"] as? String
            let attachName = attachment["name"] as? String
            let attachType = attachment["type"] as? String
            let attachSize = attachment["size"] as? Int

            if let attachUrl = attachUrl, let attachName = attachName {
                logger.info("Message has attachment: '\(attachName)' (Type: \(attachType ?? "N/A"), Size: \(attachSize ?? 0))")

                // Download attachment
                do {
                    let baseServerURL = configurationManager.receiveServerURL
                    let (contentBytes, contentTypeHeader) = try await ntfyService.downloadAttachment(from: attachUrl, baseServerURL: baseServerURL)
                    let resolvedContentType = attachType ?? contentTypeHeader

                    // Check if it's an image
                    if ntfyService.isImageAttachment(filename: attachName, contentType: resolvedContentType) {
                        if isMacOSImageSupport {
                            logger.info("Detected image attachment '\(attachName)'. Preparing to copy image (macOS)")
                            imageToCopy = contentBytes
                            imageFilename = attachName
                            copySourceDescription = "Image Attachment '\(attachName)'"
                        } else {
                            logger.info("Detected image attachment '\(attachName)'. Copying URL (non-macOS or disabled)")
                            textToCopy = attachUrl
                            copySourceDescription = "Image Attachment URL '\(attachName)'"
                        }
                    }
                    // Check if it's a text file
                    else if ntfyService.isTextAttachment(filename: attachName, contentType: resolvedContentType) {
                        logger.info("Detected text attachment '\(attachName)'. Decoding content")
                        textToCopy = ntfyService.decodeTextContent(contentBytes, url: attachUrl)
                        copySourceDescription = "Text Attachment '\(attachName)'"
                        if textToCopy == nil {
                            logger.warning("Failed to decode text attachment '\(attachName)'. Falling back to message body")
                            textToCopy = messageContent
                            copySourceDescription = "Message Body (Text attach decode failed: '\(attachName)')"
                        }
                    }
                    // Unknown attachment type
                    else {
                        logger.info("Attachment '\(attachName)' is not a recognized image or text type. Copying message body if available")
                        if !messageContent.isEmpty {
                            textToCopy = messageContent
                            copySourceDescription = "Message Body (Unknown attach type: '\(attachName)')"
                        } else {
                            logger.warning("Unknown attachment type '\(attachName)' and no message body. Nothing to copy")
                            return
                        }
                    }

                } catch {
                    logger.warning("Failed to download attachment '\(attachName)'. Falling back to message body if available")
                    if !messageContent.isEmpty {
                        textToCopy = messageContent
                        copySourceDescription = "Message Body (Attach download failed: '\(attachName)')"
                    } else {
                        logger.warning("Attachment download failed for '\(attachName)' and no message body. Nothing to copy")
                        return
                    }
                }
            } else {
                logger.warning("Message has incomplete attachment data. Copying message body if available")
                if !messageContent.isEmpty {
                    textToCopy = messageContent
                    copySourceDescription = "Message Body (Incomplete attach data)"
                } else {
                    logger.warning("Incomplete attachment data and no message body. Nothing to copy")
                    return
                }
            }
        }
        // No Attachment or Fallback
        else {
            if !messageContent.isEmpty {
                logger.info("Received message with no attachment or fell back to message body")
                textToCopy = messageContent
                copySourceDescription = "Message Body"
            } else {
                logger.info("Received message with no attachment and no message body. Nothing to copy")
                return
            }
        }

        // Perform Clipboard Action
        var copiedSuccessfully = false

        // Handle image copying
        if let imageData = imageToCopy, let filename = imageFilename, isMacOSImageSupport {
            logger.info("Attempting to copy \(copySourceDescription) to clipboard (macOS image)...")
            copiedSuccessfully = await MainActor.run {
                clipboardManager.setImageMacOS(imageData, filename: filename, source: "Receiver")
            }
            if copiedSuccessfully {
                logger.info("Successfully copied \(copySourceDescription) to clipboard")
            } else {
                logger.error("Failed to copy \(copySourceDescription) (image) to clipboard")
            }
        }

        // Handle text copying (either primary or fallback)
        if let text = textToCopy, !copiedSuccessfully {
            logger.info("Attempting to copy \(copySourceDescription) to clipboard (text)...")
            copiedSuccessfully = await MainActor.run {
                clipboardManager.setText(text, source: "Receiver")
            }
            if copiedSuccessfully {
                // Update shared state for loop prevention (matching Python)
                await appViewModel?.didReceiveContent(identifier: text.sha256)
                logger.info("Successfully copied \(copySourceDescription) to clipboard. Updated last received content")
            } else {
                logger.error("Failed to copy \(copySourceDescription) (text) to clipboard")
            }
        }
    }
}

// MARK: - ClipboardReceiver Extensions

extension ClipboardReceiver {
    /// Get current receiving status
    var isCurrentlyReceiving: Bool {
        return isReceiving
    }
}
