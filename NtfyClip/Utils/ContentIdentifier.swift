import Foundation
import CryptoKit
import AppKit

struct ContentIdentifier {
    
    /// Generate a unique identifier for clipboard content
    static func generate(for content: ClipboardContent) -> String {
        switch content.type {
        case .text:
            if let text = content.data as? String {
                return generateForText(text)
            }
        case .image:
            if let image = content.data as? NSImage {
                return generateForImage(image)
            }
        }
        
        // Fallback: use timestamp
        return generateForTimestamp(content.timestamp)
    }
    
    /// Generate identifier for text content
    static func generateForText(_ text: String) -> String {
        let data = text.data(using: .utf8) ?? Data()
        return generateSHA256(for: data)
    }
    
    /// Generate identifier for image content
    static func generateForImage(_ image: NSImage) -> String {
        guard let tiffData = image.tiffRepresentation else {
            return generateForTimestamp(Date())
        }
        return generateSHA256(for: tiffData)
    }
    
    /// Generate identifier for raw data
    static func generateForData(_ data: Data) -> String {
        return generateSHA256(for: data)
    }
    
    /// Generate identifier based on timestamp (fallback)
    static func generateForTimestamp(_ timestamp: Date) -> String {
        let timeString = String(timestamp.timeIntervalSince1970)
        return generateForText(timeString)
    }
    
    /// Generate SHA256 hash for data
    private static func generateSHA256(for data: Data) -> String {
        let hash = SHA256.hash(data: data)
        return hash.compactMap { String(format: "%02x", $0) }.joined()
    }
    
    /// Validate if an identifier is valid SHA256 format
    static func isValidIdentifier(_ identifier: String) -> Bool {
        // SHA256 should be 64 characters long and contain only hex characters
        guard identifier.count == 64 else { return false }
        
        let hexCharacterSet = CharacterSet(charactersIn: "0123456789abcdef")
        return identifier.lowercased().unicodeScalars.allSatisfy { hexCharacterSet.contains($0) }
    }
    
    /// Compare two identifiers
    static func areEqual(_ id1: String?, _ id2: String?) -> Bool {
        guard let id1 = id1, let id2 = id2 else { return false }
        return id1 == id2
    }
}

// MARK: - Content Identifier Extensions

extension String {
    /// Check if this string is a valid content identifier
    var isValidContentIdentifier: Bool {
        return ContentIdentifier.isValidIdentifier(self)
    }
}

extension ClipboardContent {
    /// Get the content identifier for this clipboard content
    var identifier: String {
        return ContentIdentifier.generate(for: self)
    }
}
