import SwiftUI
import Combine
import OSLog
import AppKit

@MainActor
class AppViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isConnected: Bool = false
    @Published var isSyncEnabled: Bool = false
    @Published var errorMessage: String?
    @Published var lastSyncTime: Date?
    @Published var clipboardItemsCount: Int = 0
    
    // MARK: - Private Properties
    private var clipboardSender: ClipboardSender?
    private var clipboardReceiver: ClipboardReceiver?
    private var lastReceivedContentId: String?
    private var cancellables = Set<AnyCancellable>()
    
    private let logger = Logger(subsystem: "com.ntfyclip.app", category: "AppViewModel")
    private let configurationManager: ConfigurationManager
    private let logManager = LogManager.shared
    
    // MARK: - Initialization
    init(configurationManager: ConfigurationManager) {
        self.configurationManager = configurationManager
        logger.info("AppViewModel initializing")
        logManager.info("AppViewModel", "Initializing AppViewModel")
        print("🔧 AppViewModel: Initializing...")
        setupConfigurationObserver()
        loadInitialConfiguration()
        print("✅ AppViewModel: Initialization complete")
        logManager.info("AppViewModel", "AppViewModel initialization complete")
    }
    
    // MARK: - Public Methods
    func startSync() {
        guard !isSyncEnabled else { return }

        logger.info("Starting sync")
        logManager.info("AppViewModel", "Starting clipboard sync")
        isSyncEnabled = true
        errorMessage = nil

        Task {
            await startBackgroundActors()
        }
    }
    
    func stopSync() {
        guard isSyncEnabled else { return }

        logger.info("Stopping sync")
        logManager.info("AppViewModel", "Stopping clipboard sync")
        isSyncEnabled = false
        isConnected = false

        Task {
            await stopBackgroundActors()
        }
    }
    
    func toggleSync() {
        if isSyncEnabled {
            stopSync()
        } else {
            startSync()
        }
    }
    
    // MARK: - Loop Prevention
    func shouldSendContent(identifier: String) -> Bool {
        let shouldSend = lastReceivedContentId != identifier
        logger.debug("Should send content with ID \(identifier): \(shouldSend)")
        return shouldSend
    }
    
    func didReceiveContent(identifier: String) {
        logger.debug("Received content with ID: \(identifier)")
        lastReceivedContentId = identifier
        lastSyncTime = Date()
        clipboardItemsCount += 1
    }
    
    // MARK: - Error Handling
    func handleError(_ error: Error) {
        logger.error("Error occurred: \(error.localizedDescription)")
        logManager.error("AppViewModel", "Error occurred: \(error.localizedDescription)")
        errorMessage = error.localizedDescription

        // If it's a critical error, stop sync
        if error is CriticalError {
            logManager.critical("AppViewModel", "Critical error encountered, stopping sync")
            stopSync()
        }
    }
    
    func clearError() {
        errorMessage = nil
    }
    
    // MARK: - Private Methods
    private func setupConfigurationObserver() {
        configurationManager.configurationChanged
            .receive(on: DispatchQueue.main)
            .sink { [weak self] in
                self?.handleConfigurationChange()
            }
            .store(in: &cancellables)
    }
    
    private func loadInitialConfiguration() {
        // Load saved sync state
        isSyncEnabled = configurationManager.isSyncEnabled
        if isSyncEnabled {
            Task {
                await startBackgroundActors()
            }
        }
    }
    
    private func handleConfigurationChange() {
        logger.info("Configuration changed, restarting sync if enabled")
        if isSyncEnabled {
            Task {
                await stopBackgroundActors()
                await startBackgroundActors()
            }
        }
    }
    
    private func startBackgroundActors() async {
        await stopBackgroundActors() // Ensure clean state

        // Create and start clipboard sender
        clipboardSender = ClipboardSender(
            appViewModel: self,
            ntfyService: NtfyService(),
            clipboardManager: ClipboardManager(),
            configurationManager: configurationManager
        )

        // Create and start clipboard receiver
        clipboardReceiver = ClipboardReceiver(
            appViewModel: self,
            ntfyService: NtfyService(),
            clipboardManager: ClipboardManager(),
            configurationManager: configurationManager
        )

        // Start monitoring
        await clipboardSender?.startMonitoring()
        await clipboardReceiver?.startReceiving()

        isConnected = true
        logger.info("Background actors started successfully")
    }
    
    private func stopBackgroundActors() async {
        if let sender = clipboardSender {
            await sender.stopMonitoring()
            clipboardSender = nil
        }
        
        if let receiver = clipboardReceiver {
            await receiver.stopReceiving()
            clipboardReceiver = nil
        }
        
        isConnected = false
        logger.info("Background actors stopped")
    }
}

// MARK: - Error Types
enum CriticalError: Error, LocalizedError {
    case invalidConfiguration
    case authenticationFailed
    case networkUnavailable
    
    var errorDescription: String? {
        switch self {
        case .invalidConfiguration:
            return "Invalid configuration. Please check your settings."
        case .authenticationFailed:
            return "Authentication failed. Please check your credentials."
        case .networkUnavailable:
            return "Network unavailable. Please check your connection."
        }
    }
}
