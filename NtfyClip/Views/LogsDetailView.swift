import SwiftUI

struct LogsDetailView: View {
    @StateObject private var logManager = LogManager.shared
    @State private var searchText = ""
    @State private var selectedLevel: LogLevel? = nil
    @State private var showingExportSheet = false
    @State private var exportedLogs = ""
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with controls
            headerSection
            
            // Log list
            logListSection
        }
        .navigationTitle("Logs")
        .sheet(isPresented: $showingExportSheet) {
            LogExportView(logs: exportedLogs)
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 12) {
            // Search and filter controls
            HStack {
                // Search field
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    
                    TextField("Search logs...", text: $searchText)
                        .textFieldStyle(.plain)
                        .onChange(of: searchText) { newValue in
                            logManager.setSearchText(newValue)
                        }
                    
                    if !searchText.isEmpty {
                        Button(action: {
                            searchText = ""
                            logManager.setSearchText("")
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                        }
                        .buttonStyle(.plain)
                    }
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color(NSColor.controlBackgroundColor))
                .cornerRadius(6)
                
                // Level filter
                Picker("Level", selection: $selectedLevel) {
                    Text("All Levels").tag(LogLevel?.none)
                    ForEach(LogLevel.allCases, id: \.self) { level in
                        Label(level.rawValue, systemImage: level.icon)
                            .tag(LogLevel?.some(level))
                    }
                }
                .pickerStyle(.menu)
                .onChange(of: selectedLevel) { newValue in
                    logManager.setLevelFilter(newValue)
                }
            }
            
            // Statistics and actions
            HStack {
                // Statistics
                HStack(spacing: 16) {
                    StatBadge(
                        title: "Total",
                        count: logManager.logs.count,
                        color: .blue
                    )
                    
                    StatBadge(
                        title: "Errors",
                        count: logManager.errorCount,
                        color: .red
                    )
                    
                    StatBadge(
                        title: "Warnings",
                        count: logManager.warningCount,
                        color: .orange
                    )
                }
                
                Spacer()
                
                // Actions
                HStack {
                    Button("Export") {
                        exportedLogs = logManager.exportLogs()
                        showingExportSheet = true
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Clear") {
                        logManager.clearLogs()
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.red)
                }
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .overlay(
            Rectangle()
                .frame(height: 1)
                .foregroundColor(Color(NSColor.separatorColor)),
            alignment: .bottom
        )
    }
    
    // MARK: - Log List Section
    private var logListSection: some View {
        Group {
            if logManager.filteredLogs.isEmpty {
                // Empty state
                VStack(spacing: 16) {
                    Image(systemName: "doc.text")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    
                    Text("No logs to display")
                        .font(.title2)
                        .foregroundColor(.secondary)
                    
                    if !searchText.isEmpty || selectedLevel != nil {
                        Text("Try adjusting your search or filter criteria")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else {
                        Text("Logs will appear here as the application runs")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color(NSColor.textBackgroundColor))
            } else {
                // Log entries
                List(logManager.filteredLogs.reversed(), id: \.id) { log in
                    LogEntryRow(log: log)
                        .listRowInsets(EdgeInsets(top: 4, leading: 12, bottom: 4, trailing: 12))
                }
                .listStyle(.plain)
                .background(Color(NSColor.textBackgroundColor))
            }
        }
    }
}

// MARK: - Log Entry Row
struct LogEntryRow: View {
    let log: LogEntry
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            // Main row
            HStack(alignment: .top, spacing: 8) {
                // Level indicator
                Image(systemName: log.level.icon)
                    .foregroundColor(log.level.color)
                    .frame(width: 16)
                
                // Content
                VStack(alignment: .leading, spacing: 2) {
                    // Message
                    Text(log.message)
                        .font(.system(.body, design: .monospaced))
                        .lineLimit(isExpanded ? nil : 3)
                        .fixedSize(horizontal: false, vertical: true)
                    
                    // Metadata
                    HStack {
                        Text(log.timestamp, style: .time)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(log.category)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(log.level.rawValue)
                            .font(.caption)
                            .foregroundColor(log.level.color)
                            .fontWeight(.medium)
                        
                        Spacer()
                        
                        if log.message.count > 100 {
                            Button(isExpanded ? "Less" : "More") {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    isExpanded.toggle()
                                }
                            }
                            .font(.caption)
                            .buttonStyle(.borderless)
                        }
                    }
                }
                
                Spacer()
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            if log.message.count > 100 {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isExpanded.toggle()
                }
            }
        }
    }
}

// MARK: - Stat Badge
struct StatBadge: View {
    let title: String
    let count: Int
    let color: Color
    
    var body: some View {
        HStack(spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("\(count)")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(color)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(color.opacity(0.1))
                .cornerRadius(4)
        }
    }
}

// MARK: - Log Export View
struct LogExportView: View {
    let logs: String
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                ScrollView {
                    Text(logs)
                        .font(.system(.caption, design: .monospaced))
                        .textSelection(.enabled)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding()
                }
                .background(Color(NSColor.textBackgroundColor))
                
                HStack {
                    Button("Copy to Clipboard") {
                        NSPasteboard.general.clearContents()
                        NSPasteboard.general.setString(logs, forType: .string)
                    }
                    .buttonStyle(.bordered)
                    
                    Spacer()
                    
                    Button("Done") {
                        dismiss()
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding()
            }
            .navigationTitle("Export Logs")
            .frame(width: 600, height: 500)
        }
    }
}
