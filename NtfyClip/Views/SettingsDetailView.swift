import SwiftUI

struct SettingsDetailView: View {
    @EnvironmentObject private var appViewModel: AppViewModel
    @EnvironmentObject private var configManager: ConfigurationManager
    @State private var showingResetAlert = false
    @State private var selectedTab: SettingsTab = .general
    
    var body: some View {
        VStack(spacing: 0) {
            // Tab Bar
            tabBar
            
            // Content
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    switch selectedTab {
                    case .general:
                        generalSettings
                    case .send:
                        sendSettings
                    case .receive:
                        receiveSettings
                    case .advanced:
                        advancedSettings
                    }
                }
                .padding()
            }
            
            // Footer
            footerSection
        }
        .navigationTitle("Settings")
    }
    
    // MARK: - Tab Bar
    private var tabBar: some View {
        HStack(spacing: 0) {
            ForEach(SettingsTab.allCases, id: \.self) { tab in
                Button(action: {
                    selectedTab = tab
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: tab.icon)
                            .font(.system(size: 16))
                        
                        Text(tab.title)
                            .font(.caption)
                    }
                    .foregroundColor(selectedTab == tab ? .accentColor : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                }
                .buttonStyle(.plain)
                .background(
                    Rectangle()
                        .fill(selectedTab == tab ? Color.accentColor.opacity(0.1) : Color.clear)
                )
            }
        }
        .background(Color(NSColor.controlBackgroundColor))
        .overlay(
            Rectangle()
                .frame(height: 1)
                .foregroundColor(Color(NSColor.separatorColor)),
            alignment: .bottom
        )
    }
    
    // MARK: - General Settings
    private var generalSettings: some View {
        VStack(alignment: .leading, spacing: 20) {
            GroupBox("Sync Options") {
                VStack(alignment: .leading, spacing: 12) {
                    Toggle("Enable Sending", isOn: $configManager.enableSending)
                        .help("Allow sending clipboard content to ntfy")
                    
                    Toggle("Enable Receiving", isOn: $configManager.enableReceiving)
                        .help("Allow receiving content from ntfy to clipboard")
                    
                    Divider()
                    
                    HStack {
                        Text("Overall Status:")
                            .fontWeight(.medium)
                        
                        Spacer()
                        
                        HStack(spacing: 6) {
                            Image(systemName: configManager.isConfigurationValid() ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundColor(configManager.isConfigurationValid() ? .green : .red)
                            
                            Text(configManager.isConfigurationValid() ? "Configuration Valid" : "Configuration Invalid")
                                .font(.caption)
                                .foregroundColor(configManager.isConfigurationValid() ? .green : .red)
                        }
                    }
                }
                .padding(.vertical, 8)
            }
            
            GroupBox("Quick Actions") {
                VStack(spacing: 12) {
                    Button(action: {
                        appViewModel.toggleSync()
                    }) {
                        HStack {
                            Image(systemName: appViewModel.isSyncEnabled ? "stop.circle.fill" : "play.circle.fill")
                            Text(appViewModel.isSyncEnabled ? "Stop Sync" : "Start Sync")
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(!configManager.isConfigurationValid())
                    
                    Button("Test Configuration") {
                        testConfiguration()
                    }
                    .buttonStyle(.bordered)
                    .disabled(!configManager.isConfigurationValid())
                }
                .padding(.vertical, 8)
            }
        }
    }
    
    // MARK: - Send Settings
    private var sendSettings: some View {
        VStack(alignment: .leading, spacing: 20) {
            GroupBox("Send Configuration") {
                VStack(alignment: .leading, spacing: 12) {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Ntfy Server URL")
                            .fontWeight(.medium)
                        
                        TextField("https://ntfy.sh", text: $configManager.sendServerURL)
                            .textFieldStyle(.roundedBorder)
                        
                        Text("The URL of your ntfy server for sending messages")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Topic Name")
                            .fontWeight(.medium)
                        
                        TextField("my-clipboard-send", text: $configManager.sendTopicName)
                            .textFieldStyle(.roundedBorder)
                        
                        Text("Topic name for sending clipboard content")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // Configuration validation status
                    HStack {
                        Image(systemName: configManager.isSendConfigurationValid() ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(configManager.isSendConfigurationValid() ? .green : .red)
                        
                        Text(configManager.isSendConfigurationValid() ? "Send configuration is valid" : "Please complete the send configuration")
                            .font(.caption)
                            .foregroundColor(configManager.isSendConfigurationValid() ? .green : .red)
                    }
                }
                .padding(.vertical, 8)
            }
            
            GroupBox("Send Authentication") {
                VStack(alignment: .leading, spacing: 12) {
                    Toggle("Use Authentication for Sending", isOn: $configManager.useSendAuthentication)
                        .toggleStyle(.checkbox)
                    
                    if configManager.useSendAuthentication {
                        VStack(alignment: .leading, spacing: 8) {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Username")
                                    .fontWeight(.medium)
                                
                                TextField("Username", text: $configManager.sendUsername)
                                    .textFieldStyle(.roundedBorder)
                            }
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Password")
                                    .fontWeight(.medium)
                                
                                SecureField("Password", text: $configManager.sendPassword)
                                    .textFieldStyle(.roundedBorder)
                            }
                        }
                        .transition(.opacity.combined(with: .move(edge: .top)))
                    }
                }
                .padding(.vertical, 8)
                .animation(.easeInOut(duration: 0.2), value: configManager.useSendAuthentication)
            }
        }
    }
    
    // MARK: - Receive Settings
    private var receiveSettings: some View {
        VStack(alignment: .leading, spacing: 20) {
            GroupBox("Receive Configuration") {
                VStack(alignment: .leading, spacing: 12) {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Ntfy Server URL")
                            .fontWeight(.medium)
                        
                        TextField("https://ntfy.sh", text: $configManager.receiveServerURL)
                            .textFieldStyle(.roundedBorder)
                        
                        Text("The URL of your ntfy server for receiving messages")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Topic Name")
                            .fontWeight(.medium)
                        
                        TextField("my-clipboard-receive", text: $configManager.receiveTopicName)
                            .textFieldStyle(.roundedBorder)
                        
                        Text("Topic name for receiving clipboard content")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // Configuration validation status
                    HStack {
                        Image(systemName: configManager.isReceiveConfigurationValid() ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(configManager.isReceiveConfigurationValid() ? .green : .red)
                        
                        Text(configManager.isReceiveConfigurationValid() ? "Receive configuration is valid" : "Please complete the receive configuration")
                            .font(.caption)
                            .foregroundColor(configManager.isReceiveConfigurationValid() ? .green : .red)
                    }
                }
                .padding(.vertical, 8)
            }
            
            GroupBox("Receive Authentication") {
                VStack(alignment: .leading, spacing: 12) {
                    Toggle("Use Authentication for Receiving", isOn: $configManager.useReceiveAuthentication)
                        .toggleStyle(.checkbox)
                    
                    if configManager.useReceiveAuthentication {
                        VStack(alignment: .leading, spacing: 8) {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Username")
                                    .fontWeight(.medium)
                                
                                TextField("Username", text: $configManager.receiveUsername)
                                    .textFieldStyle(.roundedBorder)
                            }
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Password")
                                    .fontWeight(.medium)
                                
                                SecureField("Password", text: $configManager.receivePassword)
                                    .textFieldStyle(.roundedBorder)
                            }
                        }
                        .transition(.opacity.combined(with: .move(edge: .top)))
                    }
                }
                .padding(.vertical, 8)
                .animation(.easeInOut(duration: 0.2), value: configManager.useReceiveAuthentication)
            }
        }
    }
    
    // MARK: - Advanced Settings
    private var advancedSettings: some View {
        VStack(alignment: .leading, spacing: 20) {
            GroupBox("Performance") {
                VStack(alignment: .leading, spacing: 12) {
                    Text("These settings affect performance and resource usage.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    // Placeholder for future advanced settings
                    Text("Advanced performance settings will be available in future versions.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .italic()
                }
                .padding(.vertical, 8)
            }
            
            GroupBox("Debug") {
                VStack(alignment: .leading, spacing: 12) {
                    Button("Open Log Directory") {
                        // TODO: Implement log directory opening
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Export Configuration") {
                        // TODO: Implement configuration export
                    }
                    .buttonStyle(.bordered)
                }
                .padding(.vertical, 8)
            }
        }
    }
    
    // MARK: - Footer Section
    private var footerSection: some View {
        HStack {
            Button("Reset to Defaults") {
                showingResetAlert = true
            }
            .buttonStyle(.borderless)
            .foregroundColor(.red)
            
            Spacer()
            
            Button("Save Configuration") {
                configManager.saveConfiguration()
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .alert("Reset Settings", isPresented: $showingResetAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Reset", role: .destructive) {
                configManager.resetToDefaults()
            }
        } message: {
            Text("This will reset all settings to their default values. This action cannot be undone.")
        }
    }
    
    // MARK: - Private Methods
    private func testConfiguration() {
        // TODO: Implement configuration testing
        print("Testing configuration...")
    }
}

// MARK: - Settings Tabs
enum SettingsTab: String, CaseIterable {
    case general = "General"
    case send = "Send"
    case receive = "Receive"
    case advanced = "Advanced"
    
    var title: String { rawValue }
    
    var icon: String {
        switch self {
        case .general:
            return "gearshape"
        case .send:
            return "arrow.up.circle"
        case .receive:
            return "arrow.down.circle"
        case .advanced:
            return "slider.horizontal.3"
        }
    }
}
