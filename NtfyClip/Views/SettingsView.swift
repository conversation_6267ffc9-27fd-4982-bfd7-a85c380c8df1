import SwiftUI

struct SettingsView: View {
    @EnvironmentObject private var appViewModel: AppViewModel
    @StateObject private var configManager = ConfigurationManager()
    @Environment(\.dismiss) private var dismiss
    
    @State private var showingResetAlert = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerSection
            
            // Content
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    serverConfigSection
                    authenticationSection
                    aboutSection
                }
                .padding()
            }
            
            // Footer
            footerSection
        }
        .navigationTitle("Settings")
        .frame(minWidth: 400, minHeight: 300)
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            Image(systemName: "doc.on.clipboard")
                .font(.title)
                .foregroundColor(.blue)
            
            VStack(alignment: .leading) {
                Text("NtfyClip Settings")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Configure your clipboard sync")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button("Done") {
                dismiss()
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    // MARK: - Server Configuration Section
    private var serverConfigSection: some View {
        GroupBox("Server Configuration") {
            VStack(alignment: .leading, spacing: 12) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Ntfy Server URL")
                        .font(.headline)
                    
                    TextField("https://ntfy.sh", text: $configManager.sendServerURL)
                        .textFieldStyle(.roundedBorder)
                    
                    Text("The URL of your ntfy server")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Topic Name")
                        .font(.headline)
                    
                    TextField("my-clipboard-topic", text: $configManager.sendTopicName)
                        .textFieldStyle(.roundedBorder)
                    
                    Text("A unique topic name for your clipboard sync")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // Configuration validation status
                HStack {
                    Image(systemName: configManager.isConfigurationValid() ? "checkmark.circle.fill" : "xmark.circle.fill")
                        .foregroundColor(configManager.isConfigurationValid() ? .green : .red)
                    
                    Text(configManager.isConfigurationValid() ? "Configuration is valid" : "Please complete the configuration")
                        .font(.caption)
                        .foregroundColor(configManager.isConfigurationValid() ? .green : .red)
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - Authentication Section
    private var authenticationSection: some View {
        GroupBox("Authentication (Optional)") {
            VStack(alignment: .leading, spacing: 12) {
                Toggle("Use Authentication", isOn: $configManager.useSendAuthentication)
                    .toggleStyle(.checkbox)
                
                if configManager.useSendAuthentication {
                    VStack(alignment: .leading, spacing: 8) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Username")
                                .font(.subheadline)
                            
                            TextField("Username", text: $configManager.sendUsername)
                                .textFieldStyle(.roundedBorder)
                        }
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Password")
                                .font(.subheadline)
                            
                            SecureField("Password", text: $configManager.sendPassword)
                                .textFieldStyle(.roundedBorder)
                        }
                    }
                    .transition(.opacity.combined(with: .move(edge: .top)))
                }
            }
            .padding(.vertical, 8)
            .animation(.easeInOut(duration: 0.2), value: configManager.useSendAuthentication)
        }
    }
    
    // MARK: - About Section
    private var aboutSection: some View {
        GroupBox("About") {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Version:")
                        .font(.subheadline)
                    
                    Spacer()
                    
                    Text("1.0.0")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("Build:")
                        .font(.subheadline)
                    
                    Spacer()
                    
                    Text("1")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Divider()
                
                Text("NtfyClip synchronizes your clipboard content with ntfy, enabling seamless clipboard sharing across devices.")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - Footer Section
    private var footerSection: some View {
        HStack {
            Button("Reset to Defaults") {
                showingResetAlert = true
            }
            .buttonStyle(.borderless)
            .foregroundColor(.red)
            
            Spacer()
            
            Button("Test Connection") {
                testConnection()
            }
            .buttonStyle(.bordered)
            .disabled(!configManager.isConfigurationValid())
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .alert("Reset Settings", isPresented: $showingResetAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Reset", role: .destructive) {
                configManager.resetToDefaults()
            }
        } message: {
            Text("This will reset all settings to their default values. This action cannot be undone.")
        }
    }
    
    // MARK: - Private Methods
    private func testConnection() {
        // TODO: Implement connection test
        // This would involve making a test request to the ntfy server
        print("Testing connection to \(configManager.sendServerURL)/\(configManager.sendTopicName)")
    }
}
