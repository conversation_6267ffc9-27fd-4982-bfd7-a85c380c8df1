import SwiftUI

struct StatusDetailView: View {
    @EnvironmentObject private var appViewModel: AppViewModel
    @StateObject private var configManager = ConfigurationManager()
    @StateObject private var logManager = LogManager.shared
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Header
                headerSection
                
                // Connection Status
                connectionStatusSection
                
                // Configuration Status
                configurationStatusSection
                
                // Statistics
                statisticsSection
                
                // Recent Activity
                recentActivitySection
                
                Spacer()
            }
            .padding()
        }
        .navigationTitle("Status")
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            Image(systemName: "doc.on.clipboard")
                .font(.system(size: 32))
                .foregroundColor(.blue)
            
            VStack(alignment: .leading, spacing: 4) {
                Text("NtfyClip")
                    .font(.title)
                    .fontWeight(.semibold)
                
                Text("Clipboard Sync for macOS")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Overall Status Indicator
            VStack(alignment: .trailing, spacing: 4) {
                Circle()
                    .fill(overallStatusColor)
                    .frame(width: 16, height: 16)
                    .overlay(
                        Circle()
                            .stroke(overallStatusColor.opacity(0.3), lineWidth: 3)
                            .scaleEffect(appViewModel.isConnected ? 1.5 : 1.0)
                            .opacity(appViewModel.isConnected ? 0 : 1)
                            .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: false), 
                                      value: appViewModel.isConnected)
                    )
                
                Text(overallStatusText)
                    .font(.caption)
                    .foregroundColor(overallStatusColor)
                    .fontWeight(.medium)
            }
        }
    }
    
    // MARK: - Connection Status Section
    private var connectionStatusSection: some View {
        GroupBox("Connection Status") {
            VStack(spacing: 12) {
                HStack {
                    Label("Sync Status", systemImage: "arrow.triangle.2.circlepath")
                    Spacer()
                    Text(appViewModel.isSyncEnabled ? "Enabled" : "Disabled")
                        .foregroundColor(appViewModel.isSyncEnabled ? .green : .secondary)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Label("Send Service", systemImage: "arrow.up.circle")
                    Spacer()
                    Text(configManager.enableSending ? (configManager.isSendConfigurationValid() ? "Ready" : "Invalid Config") : "Disabled")
                        .foregroundColor(sendStatusColor)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Label("Receive Service", systemImage: "arrow.down.circle")
                    Spacer()
                    Text(configManager.enableReceiving ? (configManager.isReceiveConfigurationValid() ? "Ready" : "Invalid Config") : "Disabled")
                        .foregroundColor(receiveStatusColor)
                        .fontWeight(.medium)
                }
                
                if let lastSyncTime = appViewModel.lastSyncTime {
                    HStack {
                        Label("Last Sync", systemImage: "clock")
                        Spacer()
                        Text(lastSyncTime, style: .relative)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - Configuration Status Section
    private var configurationStatusSection: some View {
        GroupBox("Configuration") {
            VStack(spacing: 12) {
                HStack {
                    Label("Send Configuration", systemImage: "paperplane")
                    Spacer()
                    Image(systemName: configManager.isSendConfigurationValid() ? "checkmark.circle.fill" : "xmark.circle.fill")
                        .foregroundColor(configManager.isSendConfigurationValid() ? .green : .red)
                }
                
                if configManager.enableSending && configManager.isSendConfigurationValid() {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Send URL: \(configManager.sendServerURL)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("Send Topic: \(configManager.sendTopicName)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                
                Divider()
                
                HStack {
                    Label("Receive Configuration", systemImage: "tray.and.arrow.down")
                    Spacer()
                    Image(systemName: configManager.isReceiveConfigurationValid() ? "checkmark.circle.fill" : "xmark.circle.fill")
                        .foregroundColor(configManager.isReceiveConfigurationValid() ? .green : .red)
                }
                
                if configManager.enableReceiving && configManager.isReceiveConfigurationValid() {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Receive URL: \(configManager.receiveServerURL)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("Receive Topic: \(configManager.receiveTopicName)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - Statistics Section
    private var statisticsSection: some View {
        GroupBox("Statistics") {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                StatisticCard(
                    title: "Items Synced",
                    value: "\(appViewModel.clipboardItemsCount)",
                    icon: "doc.on.clipboard",
                    color: .blue
                )
                
                StatisticCard(
                    title: "Uptime",
                    value: formatUptime(),
                    icon: "clock",
                    color: .green
                )
                
                StatisticCard(
                    title: "Status",
                    value: overallStatusText,
                    icon: "circle.fill",
                    color: overallStatusColor
                )
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - Recent Activity Section
    private var recentActivitySection: some View {
        GroupBox("Recent Activity") {
            VStack(alignment: .leading, spacing: 8) {
                if logManager.recentLogs.isEmpty {
                    Text("No recent activity")
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding()
                } else {
                    ForEach(logManager.recentLogs.prefix(5), id: \.id) { log in
                        HStack {
                            Image(systemName: log.level.icon)
                                .foregroundColor(log.level.color)
                                .frame(width: 16)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text(log.message)
                                    .font(.caption)
                                    .lineLimit(2)
                                
                                Text(log.timestamp, style: .time)
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                        }
                        .padding(.vertical, 2)
                        
                        if log.id != logManager.recentLogs.prefix(5).last?.id {
                            Divider()
                        }
                    }
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - Helper Properties
    private var overallStatusColor: Color {
        if appViewModel.errorMessage != nil {
            return .red
        } else if appViewModel.isConnected {
            return .green
        } else if appViewModel.isSyncEnabled {
            return .orange
        } else {
            return .gray
        }
    }
    
    private var overallStatusText: String {
        if appViewModel.errorMessage != nil {
            return "Error"
        } else if appViewModel.isConnected {
            return "Connected"
        } else if appViewModel.isSyncEnabled {
            return "Connecting"
        } else {
            return "Disabled"
        }
    }
    
    private var sendStatusColor: Color {
        if !configManager.enableSending {
            return .secondary
        } else if configManager.isSendConfigurationValid() {
            return .green
        } else {
            return .red
        }
    }
    
    private var receiveStatusColor: Color {
        if !configManager.enableReceiving {
            return .secondary
        } else if configManager.isReceiveConfigurationValid() {
            return .green
        } else {
            return .red
        }
    }
    
    private func formatUptime() -> String {
        // This would be calculated from app start time
        return "Running"
    }
}

// MARK: - Statistic Card
struct StatisticCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.title3)
                .fontWeight(.semibold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
    }
}
