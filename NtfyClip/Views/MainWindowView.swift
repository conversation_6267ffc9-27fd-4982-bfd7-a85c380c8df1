import SwiftUI

struct MainWindowView: View {
    @EnvironmentObject private var appViewModel: AppViewModel
    @StateObject private var logManager = LogManager.shared
    @State private var selectedSidebarItem: SidebarItem = .status

    var body: some View {
        NavigationSplitView {
            // Sidebar
            SidebarView(selectedItem: $selectedSidebarItem)
                .navigationSplitViewColumnWidth(min: 200, ideal: 220, max: 250)
        } detail: {
            // Detail View
            DetailView(selectedItem: selectedSidebarItem)
                .navigationSplitViewColumnWidth(min: 400, ideal: 600, max: 800)
        }
        .navigationTitle("NtfyClip")
        .frame(minWidth: 700, minHeight: 500)
    }
}

// MARK: - Sidebar Items
enum SidebarItem: String, CaseIterable, Identifiable {
    case status = "Status"
    case settings = "Settings"
    case logs = "Logs"

    var id: String { rawValue }

    var icon: String {
        switch self {
        case .status:
            return "chart.line.uptrend.xyaxis"
        case .settings:
            return "gearshape"
        case .logs:
            return "doc.text"
        }
    }
}

// MARK: - Sidebar View
struct SidebarView: View {
    @Binding var selectedItem: SidebarItem
    @EnvironmentObject private var appViewModel: AppViewModel

    var body: some View {
        List(SidebarItem.allCases, selection: $selectedItem) { item in
            Label(item.rawValue, systemImage: item.icon)
                .tag(item)
        }
        .listStyle(SidebarListStyle())
        .navigationTitle("NtfyClip")
        .toolbar {
            ToolbarItem(placement: .primaryAction) {
                Button(action: {
                    appViewModel.toggleSync()
                }) {
                    Image(systemName: appViewModel.isSyncEnabled ? "stop.circle" : "play.circle")
                }
                .help(appViewModel.isSyncEnabled ? "Stop Sync" : "Start Sync")
            }
        }
    }
}

// MARK: - Detail View
struct DetailView: View {
    let selectedItem: SidebarItem
    @EnvironmentObject private var appViewModel: AppViewModel

    var body: some View {
        Group {
            switch selectedItem {
            case .status:
                StatusDetailView()
            case .settings:
                SettingsDetailView()
            case .logs:
                LogsDetailView()
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(NSColor.controlBackgroundColor))
    }
}
