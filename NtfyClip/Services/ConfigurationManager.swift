import Foundation
import Combine
import OSLog

class ConfigurationManager: ObservableObject {
    // MARK: - Published Properties
    // Send Configuration
    @Published var sendServerURL: String = "https://ntfy.sh"
    @Published var sendTopicName: String = ""
    @Published var sendUsername: String = ""
    @Published var sendPassword: String = ""
    @Published var useSendAuthentication: Bool = false

    // Receive Configuration
    @Published var receiveServerURL: String = "https://ntfy.sh"
    @Published var receiveTopicName: String = ""
    @Published var receiveUsername: String = ""
    @Published var receivePassword: String = ""
    @Published var useReceiveAuthentication: Bool = false

    // General Configuration
    @Published var isSyncEnabled: Bool = false
    @Published var enableSending: Bool = true
    @Published var enableReceiving: Bool = true
    
    // MARK: - Configuration Change Publisher
    let configurationChanged = PassthroughSubject<Void, Never>()
    
    // MARK: - Private Properties
    private let userDefaults = UserDefaults.standard
    private let logger = Logger(subsystem: "com.ntfyclip.app", category: "ConfigurationManager")
    
    // MARK: - UserDefaults Keys
    private enum Keys {
        // Send Configuration
        static let sendServerURL = "sendServerURL"
        static let sendTopicName = "sendTopicName"
        static let sendUsername = "sendUsername"
        static let sendPassword = "sendPassword"
        static let useSendAuthentication = "useSendAuthentication"

        // Receive Configuration
        static let receiveServerURL = "receiveServerURL"
        static let receiveTopicName = "receiveTopicName"
        static let receiveUsername = "receiveUsername"
        static let receivePassword = "receivePassword"
        static let useReceiveAuthentication = "useReceiveAuthentication"

        // General Configuration
        static let isSyncEnabled = "isSyncEnabled"
        static let enableSending = "enableSending"
        static let enableReceiving = "enableReceiving"

        // Legacy Keys (for migration)
        static let legacyNtfyServerURL = "ntfyServerURL"
        static let legacyTopicName = "topicName"
        static let legacyUsername = "username"
        static let legacyPassword = "password"
        static let legacyUseAuthentication = "useAuthentication"
    }
    
    // MARK: - Initialization
    init() {
        loadConfiguration()
        setupObservers()
        logger.info("ConfigurationManager initialized")
    }
    
    // MARK: - Public Methods
    func saveConfiguration() {
        // Send Configuration
        userDefaults.set(sendServerURL, forKey: Keys.sendServerURL)
        userDefaults.set(sendTopicName, forKey: Keys.sendTopicName)
        userDefaults.set(sendUsername, forKey: Keys.sendUsername)
        userDefaults.set(sendPassword, forKey: Keys.sendPassword)
        userDefaults.set(useSendAuthentication, forKey: Keys.useSendAuthentication)

        // Receive Configuration
        userDefaults.set(receiveServerURL, forKey: Keys.receiveServerURL)
        userDefaults.set(receiveTopicName, forKey: Keys.receiveTopicName)
        userDefaults.set(receiveUsername, forKey: Keys.receiveUsername)
        userDefaults.set(receivePassword, forKey: Keys.receivePassword)
        userDefaults.set(useReceiveAuthentication, forKey: Keys.useReceiveAuthentication)

        // General Configuration
        userDefaults.set(isSyncEnabled, forKey: Keys.isSyncEnabled)
        userDefaults.set(enableSending, forKey: Keys.enableSending)
        userDefaults.set(enableReceiving, forKey: Keys.enableReceiving)

        logger.info("Configuration saved")
        configurationChanged.send()
    }
    
    func resetToDefaults() {
        // Send Configuration
        sendServerURL = "https://ntfy.sh"
        sendTopicName = ""
        sendUsername = ""
        sendPassword = ""
        useSendAuthentication = false

        // Receive Configuration
        receiveServerURL = "https://ntfy.sh"
        receiveTopicName = ""
        receiveUsername = ""
        receivePassword = ""
        useReceiveAuthentication = false

        // General Configuration
        isSyncEnabled = false
        enableSending = true
        enableReceiving = true

        saveConfiguration()
        logger.info("Configuration reset to defaults")
    }
    
    func isConfigurationValid() -> Bool {
        let sendValid = !enableSending || (isSendConfigurationValid())
        let receiveValid = !enableReceiving || (isReceiveConfigurationValid())
        let isValid = sendValid && receiveValid

        logger.debug("Configuration validation - Send: \(sendValid), Receive: \(receiveValid), Overall: \(isValid)")
        return isValid
    }

    func isSendConfigurationValid() -> Bool {
        return !sendServerURL.isEmpty &&
               !sendTopicName.isEmpty &&
               isValidURL(sendServerURL)
    }

    func isReceiveConfigurationValid() -> Bool {
        return !receiveServerURL.isEmpty &&
               !receiveTopicName.isEmpty &&
               isValidURL(receiveServerURL)
    }

    private func isValidURL(_ urlString: String) -> Bool {
        guard let url = URL(string: urlString) else { return false }
        return url.scheme == "http" || url.scheme == "https"
    }
    
    func getSendTopicURL() -> URL? {
        guard enableSending && isSendConfigurationValid() else { return nil }

        let baseURL = sendServerURL.hasSuffix("/") ? String(sendServerURL.dropLast()) : sendServerURL
        let urlString = "\(baseURL)/\(sendTopicName)"

        return URL(string: urlString)
    }

    func getReceiveTopicURL() -> URL? {
        guard enableReceiving && isReceiveConfigurationValid() else { return nil }

        let baseURL = receiveServerURL.hasSuffix("/") ? String(receiveServerURL.dropLast()) : receiveServerURL
        let urlString = "\(baseURL)/\(receiveTopicName)"

        return URL(string: urlString)
    }

    func getSendAuthenticationHeader() -> String? {
        guard useSendAuthentication, !sendUsername.isEmpty, !sendPassword.isEmpty else {
            return nil
        }

        let credentials = "\(sendUsername):\(sendPassword)"
        guard let credentialsData = credentials.data(using: .utf8) else {
            return nil
        }

        return "Basic \(credentialsData.base64EncodedString())"
    }

    func getReceiveAuthenticationHeader() -> String? {
        guard useReceiveAuthentication, !receiveUsername.isEmpty, !receivePassword.isEmpty else {
            return nil
        }

        let credentials = "\(receiveUsername):\(receivePassword)"
        guard let credentialsData = credentials.data(using: .utf8) else {
            return nil
        }

        return "Basic \(credentialsData.base64EncodedString())"
    }

    // MARK: - Legacy Support
    func getFullTopicURL() -> URL? {
        return getSendTopicURL()
    }

    func getAuthenticationHeader() -> String? {
        return getSendAuthenticationHeader()
    }
    
    // MARK: - Private Methods
    private func loadConfiguration() {
        // Check if we need to migrate from legacy configuration
        migrateLegacyConfiguration()

        // Load Send Configuration
        sendServerURL = userDefaults.string(forKey: Keys.sendServerURL) ?? "https://ntfy.sh"
        sendTopicName = userDefaults.string(forKey: Keys.sendTopicName) ?? ""
        sendUsername = userDefaults.string(forKey: Keys.sendUsername) ?? ""
        sendPassword = userDefaults.string(forKey: Keys.sendPassword) ?? ""
        useSendAuthentication = userDefaults.bool(forKey: Keys.useSendAuthentication)

        // Load Receive Configuration
        receiveServerURL = userDefaults.string(forKey: Keys.receiveServerURL) ?? "https://ntfy.sh"
        receiveTopicName = userDefaults.string(forKey: Keys.receiveTopicName) ?? ""
        receiveUsername = userDefaults.string(forKey: Keys.receiveUsername) ?? ""
        receivePassword = userDefaults.string(forKey: Keys.receivePassword) ?? ""
        useReceiveAuthentication = userDefaults.bool(forKey: Keys.useReceiveAuthentication)

        // Load General Configuration
        isSyncEnabled = userDefaults.bool(forKey: Keys.isSyncEnabled)
        enableSending = userDefaults.object(forKey: Keys.enableSending) as? Bool ?? true
        enableReceiving = userDefaults.object(forKey: Keys.enableReceiving) as? Bool ?? true

        logger.info("Configuration loaded from UserDefaults")
    }

    private func migrateLegacyConfiguration() {
        // Check if legacy configuration exists
        if let legacyURL = userDefaults.string(forKey: Keys.legacyNtfyServerURL),
           let legacyTopic = userDefaults.string(forKey: Keys.legacyTopicName),
           !legacyURL.isEmpty && !legacyTopic.isEmpty {

            logger.info("Migrating legacy configuration")

            // Migrate to both send and receive configurations
            userDefaults.set(legacyURL, forKey: Keys.sendServerURL)
            userDefaults.set(legacyTopic, forKey: Keys.sendTopicName)
            userDefaults.set(legacyURL, forKey: Keys.receiveServerURL)
            userDefaults.set(legacyTopic, forKey: Keys.receiveTopicName)

            // Migrate authentication if exists
            if let legacyUsername = userDefaults.string(forKey: Keys.legacyUsername),
               let legacyPassword = userDefaults.string(forKey: Keys.legacyPassword) {
                userDefaults.set(legacyUsername, forKey: Keys.sendUsername)
                userDefaults.set(legacyPassword, forKey: Keys.sendPassword)
                userDefaults.set(legacyUsername, forKey: Keys.receiveUsername)
                userDefaults.set(legacyPassword, forKey: Keys.receivePassword)

                let legacyUseAuth = userDefaults.bool(forKey: Keys.legacyUseAuthentication)
                userDefaults.set(legacyUseAuth, forKey: Keys.useSendAuthentication)
                userDefaults.set(legacyUseAuth, forKey: Keys.useReceiveAuthentication)
            }

            // Remove legacy keys
            userDefaults.removeObject(forKey: Keys.legacyNtfyServerURL)
            userDefaults.removeObject(forKey: Keys.legacyTopicName)
            userDefaults.removeObject(forKey: Keys.legacyUsername)
            userDefaults.removeObject(forKey: Keys.legacyPassword)
            userDefaults.removeObject(forKey: Keys.legacyUseAuthentication)

            logger.info("Legacy configuration migration completed")
        }
    }
    
    private func setupObservers() {
        // Observe changes to send configuration
        Publishers.CombineLatest4(
            $sendServerURL.dropFirst(),
            $sendTopicName.dropFirst(),
            $sendUsername.dropFirst(),
            $sendPassword.dropFirst()
        )
        .debounce(for: .milliseconds(500), scheduler: DispatchQueue.main)
        .sink { [weak self] _, _, _, _ in
            self?.saveConfiguration()
        }
        .store(in: &cancellables)

        // Observe changes to receive configuration
        Publishers.CombineLatest4(
            $receiveServerURL.dropFirst(),
            $receiveTopicName.dropFirst(),
            $receiveUsername.dropFirst(),
            $receivePassword.dropFirst()
        )
        .debounce(for: .milliseconds(500), scheduler: DispatchQueue.main)
        .sink { [weak self] _, _, _, _ in
            self?.saveConfiguration()
        }
        .store(in: &cancellables)

        // Observe changes to authentication and general settings
        Publishers.CombineLatest4(
            $useSendAuthentication.dropFirst(),
            $useReceiveAuthentication.dropFirst(),
            $isSyncEnabled.dropFirst(),
            Publishers.CombineLatest($enableSending.dropFirst(), $enableReceiving.dropFirst())
        )
        .sink { [weak self] _, _, _, _ in
            self?.saveConfiguration()
        }
        .store(in: &cancellables)
    }
    
    private var cancellables = Set<AnyCancellable>()
}

// MARK: - Configuration Validation Errors
enum ConfigurationError: Error, LocalizedError {
    case invalidServerURL
    case emptyTopicName
    case invalidCredentials
    
    var errorDescription: String? {
        switch self {
        case .invalidServerURL:
            return "Invalid server URL. Please enter a valid URL."
        case .emptyTopicName:
            return "Topic name cannot be empty."
        case .invalidCredentials:
            return "Invalid username or password."
        }
    }
}
