import Foundation
import SwiftUI
import OSLog

// MARK: - Log Entry
struct LogEntry: Identifiable, Equatable {
    let id = UUID()
    let timestamp: Date
    let level: LogLevel
    let category: String
    let message: String
    
    static func == (lhs: LogEntry, rhs: LogEntry) -> Bool {
        lhs.id == rhs.id
    }
}

// MARK: - Log Level
enum LogLevel: String, CaseIterable {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "WARNING"
    case error = "ERROR"
    case critical = "CRITICAL"
    
    var color: Color {
        switch self {
        case .debug:
            return .secondary
        case .info:
            return .blue
        case .warning:
            return .orange
        case .error:
            return .red
        case .critical:
            return .purple
        }
    }
    
    var icon: String {
        switch self {
        case .debug:
            return "ladybug"
        case .info:
            return "info.circle"
        case .warning:
            return "exclamationmark.triangle"
        case .error:
            return "xmark.circle"
        case .critical:
            return "exclamationmark.octagon"
        }
    }
}

// MARK: - Log Manager
@MainActor
class LogManager: ObservableObject {
    static let shared = LogManager()
    
    @Published var logs: [LogEntry] = []
    @Published var filteredLogs: [LogEntry] = []
    @Published var selectedLevel: LogLevel? = nil
    @Published var searchText: String = ""
    
    private let maxLogEntries = 1000
    private let logger = Logger(subsystem: "com.ntfyclip.app", category: "LogManager")
    
    private init() {
        setupFiltering()
        log(.info, category: "LogManager", message: "Log manager initialized")
    }
    
    // MARK: - Public Methods
    func log(_ level: LogLevel, category: String, message: String) {
        let entry = LogEntry(
            timestamp: Date(),
            level: level,
            category: category,
            message: message
        )
        
        // Add to logs array
        logs.append(entry)
        
        // Maintain max entries
        if logs.count > maxLogEntries {
            logs.removeFirst(logs.count - maxLogEntries)
        }
        
        // Log to system logger as well
        switch level {
        case .debug:
            logger.debug("\(category): \(message)")
        case .info:
            logger.info("\(category): \(message)")
        case .warning:
            logger.warning("\(category): \(message)")
        case .error:
            logger.error("\(category): \(message)")
        case .critical:
            logger.critical("\(category): \(message)")
        }
        
        // Update filtered logs
        updateFilteredLogs()
    }
    
    func clearLogs() {
        logs.removeAll()
        updateFilteredLogs()
        log(.info, category: "LogManager", message: "Logs cleared")
    }
    
    func exportLogs() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .medium
        
        var exportString = "NtfyClip Logs Export\n"
        exportString += "Generated: \(formatter.string(from: Date()))\n"
        exportString += "Total Entries: \(logs.count)\n\n"
        
        for log in logs {
            exportString += "[\(formatter.string(from: log.timestamp))] "
            exportString += "[\(log.level.rawValue)] "
            exportString += "[\(log.category)] "
            exportString += "\(log.message)\n"
        }
        
        return exportString
    }
    
    // MARK: - Computed Properties
    var recentLogs: [LogEntry] {
        Array(logs.suffix(10))
    }
    
    var errorCount: Int {
        logs.filter { $0.level == .error || $0.level == .critical }.count
    }
    
    var warningCount: Int {
        logs.filter { $0.level == .warning }.count
    }
    
    // MARK: - Private Methods
    private func setupFiltering() {
        // This would be set up with Combine publishers in a real implementation
        updateFilteredLogs()
    }
    
    private func updateFilteredLogs() {
        var filtered = logs
        
        // Filter by level
        if let selectedLevel = selectedLevel {
            filtered = filtered.filter { $0.level == selectedLevel }
        }
        
        // Filter by search text
        if !searchText.isEmpty {
            filtered = filtered.filter { log in
                log.message.localizedCaseInsensitiveContains(searchText) ||
                log.category.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        filteredLogs = filtered
    }
    
    func setLevelFilter(_ level: LogLevel?) {
        selectedLevel = level
        updateFilteredLogs()
    }
    
    func setSearchText(_ text: String) {
        searchText = text
        updateFilteredLogs()
    }
}

// MARK: - Convenience Extensions
extension LogManager {
    func debug(_ category: String, _ message: String) {
        log(.debug, category: category, message: message)
    }
    
    func info(_ category: String, _ message: String) {
        log(.info, category: category, message: message)
    }
    
    func warning(_ category: String, _ message: String) {
        log(.warning, category: category, message: message)
    }
    
    func error(_ category: String, _ message: String) {
        log(.error, category: category, message: message)
    }
    
    func critical(_ category: String, _ message: String) {
        log(.critical, category: category, message: message)
    }
}
