import Foundation
import OSLog
import AppKit

struct NtfyService {
    private let logger = Logger(subsystem: "com.ntfyclip.app", category: "NtfyService")
    private let urlSession: URLSession
    
    init(urlSession: URLSession = .shared) {
        self.urlSession = urlSession
    }
    
    // MARK: - Sending Messages
    
    /// Send text content to ntfy topic
    func sendText(_ text: String, to topicURL: URL, authHeader: String? = nil) async throws {
        var request = URLRequest(url: topicURL)
        request.httpMethod = "POST"
        request.setValue("text/plain; charset=utf-8", forHTTPHeaderField: "Content-Type")
        request.setValue("NtfyClip/1.0", forHTTPHeaderField: "User-Agent")
        
        if let authHeader = authHeader {
            request.setValue(authHeader, forHTTPHeaderField: "Authorization")
        }
        
        request.httpBody = text.data(using: .utf8)
        
        logger.debug("Sending text to ntfy: \(topicURL.absoluteString)")
        
        let (_, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw NtfyError.invalidResponse
        }
        
        guard 200...299 ~= httpResponse.statusCode else {
            logger.error("HTTP error: \(httpResponse.statusCode)")
            throw NtfyError.httpError(httpResponse.statusCode)
        }
        
        logger.info("Successfully sent text to ntfy")
    }
    
    /// Send image content to ntfy topic
    func sendImage(_ imageData: Data, to topicURL: URL, authHeader: String? = nil) async throws {
        var request = URLRequest(url: topicURL)
        request.httpMethod = "POST"
        request.setValue("image/tiff", forHTTPHeaderField: "Content-Type")
        request.setValue("NtfyClip/1.0", forHTTPHeaderField: "User-Agent")
        request.setValue("clipboard-image.tiff", forHTTPHeaderField: "Filename")
        
        if let authHeader = authHeader {
            request.setValue(authHeader, forHTTPHeaderField: "Authorization")
        }
        
        request.httpBody = imageData
        
        logger.debug("Sending image to ntfy: \(topicURL.absoluteString)")
        
        let (_, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw NtfyError.invalidResponse
        }
        
        guard 200...299 ~= httpResponse.statusCode else {
            logger.error("HTTP error: \(httpResponse.statusCode)")
            throw NtfyError.httpError(httpResponse.statusCode)
        }
        
        logger.info("Successfully sent image to ntfy")
    }
    
    /// Send clipboard content to ntfy topic
    func sendClipboardContent(_ content: ClipboardContent, to topicURL: URL, authHeader: String? = nil) async throws {
        switch content.type {
        case .text:
            if let text = content.data as? String {
                try await sendText(text, to: topicURL, authHeader: authHeader)
            } else {
                throw NtfyError.invalidContentType
            }
        case .image:
            if let image = content.data as? NSImage,
               let tiffData = image.tiffRepresentation {
                try await sendImage(tiffData, to: topicURL, authHeader: authHeader)
            } else {
                throw NtfyError.invalidContentType
            }
        }
    }

    /// Send clipboard content using configuration manager
    func sendClipboardContent(_ content: ClipboardContent, using configManager: ConfigurationManager) async throws {
        guard configManager.enableSending,
              let topicURL = configManager.getSendTopicURL() else {
            throw NtfyError.invalidURL
        }

        let authHeader = configManager.getSendAuthenticationHeader()
        try await sendClipboardContent(content, to: topicURL, authHeader: authHeader)
    }
    
    // MARK: - Receiving Messages (Polling)
    
    /// Poll for new messages from ntfy topic
    func pollMessages(from topicURL: URL, authHeader: String? = nil, since: String? = nil) async throws -> [NtfyMessage] {
        var components = URLComponents(url: topicURL, resolvingAgainstBaseURL: false)
        
        var queryItems: [URLQueryItem] = [
            URLQueryItem(name: "poll", value: "1"),
            URLQueryItem(name: "json", value: "1")
        ]
        
        if let since = since {
            queryItems.append(URLQueryItem(name: "since", value: since))
        }
        
        components?.queryItems = queryItems
        
        guard let pollURL = components?.url else {
            throw NtfyError.invalidURL
        }
        
        var request = URLRequest(url: pollURL)
        request.setValue("NtfyClip/1.0", forHTTPHeaderField: "User-Agent")
        
        if let authHeader = authHeader {
            request.setValue(authHeader, forHTTPHeaderField: "Authorization")
        }
        
        logger.debug("Polling messages from ntfy: \(pollURL.absoluteString)")
        
        let (data, response) = try await urlSession.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw NtfyError.invalidResponse
        }
        
        guard 200...299 ~= httpResponse.statusCode else {
            logger.error("HTTP error: \(httpResponse.statusCode)")
            throw NtfyError.httpError(httpResponse.statusCode)
        }
        
        // Parse JSONL (JSON Lines) response
        let messages = try parseJSONLResponse(data)
        logger.info("Received \(messages.count) messages from ntfy")
        
        return messages
    }
    
    // MARK: - Private Methods
    
    private func parseJSONLResponse(_ data: Data) throws -> [NtfyMessage] {
        guard let responseString = String(data: data, encoding: .utf8) else {
            throw NtfyError.invalidResponse
        }
        
        let lines = responseString.components(separatedBy: .newlines)
        var messages: [NtfyMessage] = []
        
        for line in lines {
            guard !line.isEmpty else { continue }
            
            if let lineData = line.data(using: .utf8) {
                do {
                    let message = try JSONDecoder().decode(NtfyMessage.self, from: lineData)
                    messages.append(message)
                } catch {
                    logger.warning("Failed to parse message line: \(line)")
                }
            }
        }
        
        return messages
    }

    /// Poll for new messages using configuration manager
    func pollMessages(using configManager: ConfigurationManager, since: String? = nil) async throws -> [NtfyMessage] {
        guard configManager.enableReceiving,
              let topicURL = configManager.getReceiveTopicURL() else {
            throw NtfyError.invalidURL
        }

        let authHeader = configManager.getReceiveAuthenticationHeader()
        return try await pollMessages(from: topicURL, authHeader: authHeader, since: since)
    }
}

// MARK: - Supporting Types

struct NtfyMessage: Codable {
    let id: String
    let time: Int64
    let expires: Int64?
    let event: String
    let topic: String
    let message: String?
    let title: String?
    let tags: [String]?
    let priority: Int?
    let attachment: NtfyAttachment?

    var date: Date {
        return Date(timeIntervalSince1970: TimeInterval(time))
    }

    var isMessage: Bool {
        return event == "message"
    }
}

struct NtfyAttachment: Codable {
    let name: String?
    let type: String?
    let size: Int64?
    let expires: Int64?
    let url: String?
}

// MARK: - Error Types

enum NtfyError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case invalidContentType
    case httpError(Int)
    case networkError(Error)
    case authenticationRequired
    case rateLimited
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid ntfy URL"
        case .invalidResponse:
            return "Invalid response from ntfy server"
        case .invalidContentType:
            return "Invalid content type for ntfy message"
        case .httpError(let code):
            return "HTTP error: \(code)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .authenticationRequired:
            return "Authentication required"
        case .rateLimited:
            return "Rate limited by ntfy server"
        }
    }
}
