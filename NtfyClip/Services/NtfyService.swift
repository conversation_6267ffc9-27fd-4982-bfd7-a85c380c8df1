import Foundation
import OSLog
import AppKit

class NtfyService {
    private let urlSession: URLSession
    private let logger = Logger(subsystem: "com.ntfyclip.app", category: "NtfyService")

    // MARK: - Configuration
    private let requestTimeout: TimeInterval = 15.0
    private let filenamePrefix = "clipboard_content_"

    // MARK: - Image UTI Mapping (matching Python implementation)
    private let imageUTIMap: [String: String] = [
        ".png": "public.png",
        ".jpg": "public.jpeg",
        ".jpeg": "public.jpeg",
        ".gif": "com.compuserve.gif",
        ".bmp": "com.microsoft.bmp",
        ".tiff": "public.tiff",
        ".tif": "public.tiff"
    ]

    // MARK: - Initialization
    init() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = requestTimeout
        config.timeoutIntervalForResource = requestTimeout * 2
        self.urlSession = URLSession(configuration: config)
    }

    // MARK: - File-based Text Sending (matching Python post_text_as_file)

    /// Send text content as file attachment to ntfy topic (matching Python implementation)
    func sendTextAsFile(_ text: String, to topicURL: URL, authHeader: String? = nil) async throws {
        guard !text.isEmpty else {
            logger.warning("Attempted to send empty text content")
            return
        }

        let tempFileURL = try createTemporaryTextFile(content: text)
        defer {
            // Clean up temporary file
            try? FileManager.default.removeItem(at: tempFileURL)
            logger.debug("Temporary file deleted: \(tempFileURL.path)")
        }

        let filename = tempFileURL.lastPathComponent
        let safeFilename = filename.addingPercentEncoding(withAllowedCharacters: .alphanumerics) ?? "clipboard.txt"

        // Read file content
        let fileData = try Data(contentsOf: tempFileURL)

        // Create request with proper headers (matching Python implementation)
        var request = URLRequest(url: topicURL)
        request.httpMethod = "POST"
        request.setValue("text/plain; charset=utf-8", forHTTPHeaderField: "Content-Type")
        request.setValue("NtfyClip/1.0", forHTTPHeaderField: "User-Agent")
        request.setValue(safeFilename, forHTTPHeaderField: "Filename")
        request.setValue("Clipboard Text (\(getCurrentTimeString()))", forHTTPHeaderField: "Title")

        if let authHeader = authHeader {
            request.setValue(authHeader, forHTTPHeaderField: "Authorization")
        }

        request.httpBody = fileData

        logger.info("Attempting to POST file: \(safeFilename) (\(fileData.count) bytes) to \(topicURL.absoluteString)")

        do {
            let (data, response) = try await urlSession.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw NtfyError.invalidResponse
            }

            if 200...299 ~= httpResponse.statusCode {
                logger.info("Successfully POSTed to ntfy. Status: \(httpResponse.statusCode)")
            } else {
                let responseText = String(data: data, encoding: .utf8) ?? "No response body"
                logger.error("Error POSTing to ntfy. Status: \(httpResponse.statusCode)")
                logger.error("Ntfy Response: \(String(responseText.prefix(500)))")
                throw NtfyError.httpError(httpResponse.statusCode, responseText)
            }
        } catch let error as NtfyError {
            throw error
        } catch {
            logger.error("Network error during POST: \(error.localizedDescription)")
            throw NtfyError.networkError(error)
        }
    }

    /// Send clipboard content using configuration manager (updated to use file-based sending)
    func sendClipboardContent(_ content: ClipboardContent, using configManager: ConfigurationManager) async throws {
        guard configManager.enableSending,
              let topicURL = configManager.getSendTopicURL() else {
            throw NtfyError.invalidURL
        }

        let authHeader = configManager.getSendAuthenticationHeader()

        switch content.type {
        case .text:
            if let text = content.data as? String {
                try await sendTextAsFile(text, to: topicURL, authHeader: authHeader)
            } else {
                throw NtfyError.invalidContentType
            }
        case .image:
            if let image = content.data as? NSImage,
               let tiffData = image.tiffRepresentation {
                try await sendImage(tiffData, to: topicURL, authHeader: authHeader)
            } else {
                throw NtfyError.invalidContentType
            }
        }
    }

    /// Send image data to ntfy topic (for backward compatibility)
    func sendImage(_ imageData: Data, to topicURL: URL, authHeader: String? = nil) async throws {
        var request = URLRequest(url: topicURL)
        request.httpMethod = "POST"
        request.setValue("image/tiff", forHTTPHeaderField: "Content-Type")
        request.setValue("NtfyClip/1.0", forHTTPHeaderField: "User-Agent")
        request.setValue("clipboard-image.tiff", forHTTPHeaderField: "Filename")

        if let authHeader = authHeader {
            request.setValue(authHeader, forHTTPHeaderField: "Authorization")
        }

        request.httpBody = imageData

        logger.debug("Sending image to ntfy: \(topicURL.absoluteString)")

        let (_, response) = try await urlSession.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw NtfyError.invalidResponse
        }

        guard 200...299 ~= httpResponse.statusCode else {
            logger.error("HTTP error: \(httpResponse.statusCode)")
            throw NtfyError.httpError(httpResponse.statusCode, nil)
        }

        logger.info("Successfully sent image to ntfy")
    }

    // MARK: - WebSocket Connection for Receiving (matching Python implementation)

    /// Create WebSocket URL from topic URL (matching Python get_websocket_url)
    func createWebSocketURL(from topicURL: URL) -> URL? {
        var components = URLComponents(url: topicURL, resolvingAgainstBaseURL: false)

        // Convert HTTP(S) to WS(S)
        if components?.scheme == "https" {
            components?.scheme = "wss"
        } else if components?.scheme == "http" {
            components?.scheme = "ws"
        }

        // Add /ws to the path
        if let path = components?.path, !path.isEmpty {
            components?.path = path + "/ws"
        } else {
            components?.path = "/ws"
        }

        return components?.url
    }

    /// Connect to ntfy WebSocket for receiving messages
    func connectWebSocket(to topicURL: URL, authHeader: String? = nil) async throws -> URLSessionWebSocketTask {
        guard let wsURL = createWebSocketURL(from: topicURL) else {
            throw NtfyError.invalidURL
        }

        var request = URLRequest(url: wsURL)
        request.setValue("NtfyClip/1.0", forHTTPHeaderField: "User-Agent")

        if let authHeader = authHeader {
            request.setValue(authHeader, forHTTPHeaderField: "Authorization")
        }

        logger.info("Attempting to connect to WebSocket: \(wsURL.absoluteString)")

        let webSocketTask = urlSession.webSocketTask(with: request)
        webSocketTask.resume()

        return webSocketTask
    }

    // MARK: - Attachment Downloading (matching Python download_attachment)

    /// Download attachment content and detect content type
    func downloadAttachment(from url: String, baseServerURL: String?) async throws -> (Data, String?) {
        let fullURL = resolveAttachmentURL(url, baseServerURL: baseServerURL)

        guard let attachmentURL = URL(string: fullURL) else {
            logger.error("Could not resolve attachment URL: \(url)")
            throw NtfyError.invalidURL
        }

        logger.info("Attempting to download attachment from: \(fullURL)")

        do {
            let (data, response) = try await urlSession.data(from: attachmentURL)

            let contentType = (response as? HTTPURLResponse)?.value(forHTTPHeaderField: "Content-Type")?.lowercased()

            logger.info("Successfully downloaded attachment. Size: \(data.count) bytes, Type: \(contentType ?? "Unknown")")

            return (data, contentType)
        } catch {
            logger.error("Network error downloading attachment: \(error.localizedDescription) from \(fullURL)")
            throw NtfyError.networkError(error)
        }
    }

    // MARK: - Content Type Detection (matching Python implementation)

    /// Check if attachment is an image based on filename or content type
    func isImageAttachment(filename: String?, contentType: String?) -> Bool {
        // Check by filename extension
        if let filename = filename {
            let lowerName = filename.lowercased()
            let fileExt = (lowerName as NSString).pathExtension
            if !fileExt.isEmpty && imageUTIMap[".\(fileExt)"] != nil {
                logger.debug("Detected image by extension '.\(fileExt)' in filename '\(filename)'")
                return true
            }
        }

        // Check by content type
        if let contentType = contentType, contentType.hasPrefix("image/") {
            logger.debug("Detected image by Content-Type '\(contentType)'")
            return true
        }

        return false
    }

    /// Check if attachment is a text file
    func isTextAttachment(filename: String?, contentType: String?) -> Bool {
        // Check by filename extension
        if let filename = filename, filename.lowercased().hasSuffix(".txt") {
            logger.debug("Detected text file by extension '.txt' in filename '\(filename)'")
            return true
        }

        // Check by content type
        if let contentType = contentType, contentType.hasPrefix("text/plain") {
            logger.debug("Detected text file by Content-Type '\(contentType)'")
            return true
        }

        return false
    }

    /// Decode text content from bytes using multiple encodings (matching Python implementation)
    func decodeTextContent(_ data: Data, url: String = "N/A") -> String? {
        guard !data.isEmpty else { return "" }

        let encodingsToTry: [String.Encoding] = [.utf8, .utf16, .ascii, .isoLatin1]

        for encoding in encodingsToTry {
            if let text = String(data: data, encoding: encoding) {
                logger.info("Successfully decoded text attachment using '\(encoding)'. URL: \(url)")
                return text
            }
        }

        // Force decode with UTF-8 ignoring errors as fallback
        logger.warning("All decoding attempts failed for attachment from \(url). Forcing decode with UTF-8 (ignore errors)")
        return String(data: data, encoding: .utf8) ?? ""
    }

    // MARK: - Private Helper Methods

    /// Create temporary text file (matching Python implementation)
    private func createTemporaryTextFile(content: String) throws -> URL {
        let tempDir = FileManager.default.temporaryDirectory
        let filename = "\(filenamePrefix)\(UUID().uuidString).txt"
        let tempFileURL = tempDir.appendingPathComponent(filename)

        try content.write(to: tempFileURL, atomically: true, encoding: .utf8)
        logger.debug("Text content written to temporary file: \(tempFileURL.path)")

        return tempFileURL
    }

    /// Get current time string for titles
    private func getCurrentTimeString() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter.string(from: Date())
    }

    /// Resolve potentially relative attachment URLs (matching Python _resolve_url)
    private func resolveAttachmentURL(_ url: String, baseServerURL: String?) -> String {
        guard !url.isEmpty else { return url }

        // Already absolute URL
        if url.hasPrefix("http://") || url.hasPrefix("https://") {
            return url
        }

        guard let baseServerURL = baseServerURL else {
            logger.error("Cannot resolve relative URL because base server URL is not provided")
            return url
        }

        var baseURL = baseServerURL
        if baseURL.hasPrefix("http://") {
            baseURL = String(baseURL.dropFirst(7)) // Remove "http://"
        } else if baseURL.hasPrefix("https://") {
            baseURL = String(baseURL.dropFirst(8)) // Remove "https://"
        }

        // Assume HTTPS by default
        let fullBaseURL = "https://\(baseURL)"

        if url.hasPrefix("//") {
            return "https:\(url)"
        } else if url.hasPrefix("/") {
            return "\(fullBaseURL)\(url)"
        } else {
            logger.warning("Ambiguous relative URL '\(url)'. Assuming relative to server root: \(fullBaseURL)/\(url)")
            return "\(fullBaseURL)/\(url)"
        }
    }
}

// MARK: - Supporting Types

struct NtfyMessage: Codable {
    let id: String
    let time: Int64
    let expires: Int64?
    let event: String
    let topic: String
    let message: String?
    let title: String?
    let tags: [String]?
    let priority: Int?
    let attachment: NtfyAttachment?

    var date: Date {
        return Date(timeIntervalSince1970: TimeInterval(time))
    }

    var isMessage: Bool {
        return event == "message"
    }
}

struct NtfyAttachment: Codable {
    let name: String?
    let type: String?
    let size: Int64?
    let expires: Int64?
    let url: String?
}

// MARK: - Error Types

enum NtfyError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case invalidContentType
    case httpError(Int, String?)
    case networkError(Error)
    case authenticationRequired
    case rateLimited
    case fileCreationError
    case decodingError(Error)
    case webSocketError(Error)
    case connectionTimeout

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid ntfy URL"
        case .invalidResponse:
            return "Invalid response from ntfy server"
        case .invalidContentType:
            return "Invalid content type for ntfy message"
        case .httpError(let code, let message):
            return "HTTP error \(code): \(message ?? "Unknown error")"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .authenticationRequired:
            return "Authentication required"
        case .rateLimited:
            return "Rate limited by ntfy server"
        case .fileCreationError:
            return "Failed to create temporary file"
        case .decodingError(let error):
            return "Failed to decode response: \(error.localizedDescription)"
        case .webSocketError(let error):
            return "WebSocket error: \(error.localizedDescription)"
        case .connectionTimeout:
            return "Connection timeout"
        }
    }
}
