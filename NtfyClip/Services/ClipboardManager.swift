import AppKit
import OSLog

@MainActor
class ClipboardManager {
    private let pasteboard = NSPasteboard.general
    private let logger = Logger(subsystem: "com.ntfyclip.app", category: "ClipboardManager")
    
    // MARK: - Public Methods
    
    /// Get the current change count of the pasteboard
    func getChangeCount() -> Int {
        return pasteboard.changeCount
    }
    
    /// Get text content from the clipboard
    func getText() -> String? {
        let text = pasteboard.string(forType: .string)
        if let text = text, !text.isEmpty {
            logger.debug("Retrieved text from clipboard: \(text.prefix(50))...")
            return text
        }
        return nil
    }
    
    /// Set text content to the clipboard
    func setText(_ text: String) {
        pasteboard.clearContents()
        pasteboard.setString(text, forType: .string)
        logger.debug("Set text to clipboard: \(text.prefix(50))...")
    }
    
    /// Get image content from the clipboard
    func getImage() -> NSImage? {
        guard let imageData = pasteboard.data(forType: .tiff),
              let image = NSImage(data: imageData) else {
            return nil
        }
        
        logger.debug("Retrieved image from clipboard")
        return image
    }
    
    /// Set image content to the clipboard
    func setImage(_ image: NSImage) {
        guard let tiffData = image.tiffRepresentation else {
            logger.error("Failed to get TIFF representation of image")
            return
        }
        
        pasteboard.clearContents()
        pasteboard.setData(tiffData, forType: .tiff)
        logger.debug("Set image to clipboard")
    }
    
    /// Get the current clipboard content as ClipboardContent
    func getCurrentContent() -> ClipboardContent? {
        // Check for text first
        if let text = getText() {
            return ClipboardContent(type: .text, data: text)
        }
        
        // Check for image
        if let image = getImage() {
            return ClipboardContent(type: .image, data: image)
        }
        
        return nil
    }
    
    /// Set clipboard content from ClipboardContent
    func setContent(_ content: ClipboardContent) {
        switch content.type {
        case .text:
            if let text = content.data as? String {
                setText(text)
            }
        case .image:
            if let image = content.data as? NSImage {
                setImage(image)
            }
        }
    }
    
    /// Check if clipboard has changed since the last check
    func hasChanged(since lastChangeCount: Int) -> Bool {
        return getChangeCount() != lastChangeCount
    }
    
    /// Get clipboard content as Data for transmission
    func getContentAsData() -> Data? {
        if let text = getText() {
            return text.data(using: .utf8)
        }
        
        if let image = getImage(),
           let tiffData = image.tiffRepresentation {
            return tiffData
        }
        
        return nil
    }
    
    /// Set clipboard content from Data
    func setContentFromData(_ data: Data, type: ClipboardContentType) {
        switch type {
        case .text:
            if let text = String(data: data, encoding: .utf8) {
                setText(text)
            }
        case .image:
            if let image = NSImage(data: data) {
                setImage(image)
            }
        }
    }
}

// MARK: - Supporting Types

enum ClipboardContentType: String, CaseIterable {
    case text = "text"
    case image = "image"
    
    var mimeType: String {
        switch self {
        case .text:
            return "text/plain"
        case .image:
            return "image/tiff"
        }
    }
}

struct ClipboardContent {
    let type: ClipboardContentType
    let data: Any
    let timestamp: Date
    
    init(type: ClipboardContentType, data: Any) {
        self.type = type
        self.data = data
        self.timestamp = Date()
    }
    
    /// Generate a unique identifier for this content
    func generateIdentifier() -> String {
        return ContentIdentifier.generate(for: self)
    }
}

// MARK: - Clipboard Errors

enum ClipboardError: Error, LocalizedError {
    case noContent
    case unsupportedContentType
    case failedToReadContent
    case failedToSetContent
    
    var errorDescription: String? {
        switch self {
        case .noContent:
            return "No content available in clipboard"
        case .unsupportedContentType:
            return "Unsupported content type"
        case .failedToReadContent:
            return "Failed to read clipboard content"
        case .failedToSetContent:
            return "Failed to set clipboard content"
        }
    }
}
