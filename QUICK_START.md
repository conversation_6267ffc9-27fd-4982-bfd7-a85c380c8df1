# NtfyClip for macOS - Quick Start Guide

## System Requirements

- macOS 13.0 (Ventura) or later
- Xcode 14.0+ or Swift 5.7+ command-line tools

## Installation and Running

### Using the Makefile (Recommended)

1.  **Clone the project**
    ```bash
    git clone https://github.com/your-username/NtfyClip.git
    cd NtfyClip
    ```

2.  **Build the project**
    ```bash
    make build
    ```

3.  **Run the application**
    ```bash
    make run
    ```

### Using Xcode

1.  Open `NtfyClip.xcodeproj` in Xcode.
2.  Wait for dependencies to resolve.
3.  Select the `NtfyClip` scheme.
4.  Click the Run button (⌘+R).

## First-Time Setup

1.  **Launch the app**. The main window will appear.
2.  **Navigate to the Settings tab**.
3.  **Configure your ntfy server(s)**:
    -   You can set up separate configurations for sending and receiving.
    -   **Server URL**: `https://ntfy.sh` (or your self-hosted server).
    -   **Topic Name**: A unique topic name, e.g., `my-clipboard-sync`.
4.  **Optional: Configure Authentication** (if your server requires it).
5.  **Click "Save Configuration"**.

## How to Use

1.  **Start Sync**: Navigate to the Status tab and click "Start Sync".
2.  **Test Sending**: Copy any text to your clipboard. It will be automatically sent to your ntfy topic.
3.  **Test Receiving**: Send a message from another device or the ntfy web interface to your topic.
4.  **Check Status**: The Status tab shows the connection status and sync statistics.

## Key Features

### Main Window
- **Status**: Shows connection status, sync stats, and recent activity.
- **Settings**: Configure send/receive servers, topics, and authentication.
- **Logs**: View detailed application logs for debugging.

### Menu Bar
- Quick access to start/stop sync and open the main window.

## Troubleshooting

- **Problem: App doesn't start.**
  - **Solution**: Ensure you have Xcode and the command-line tools installed. Try running `make clean` and then `make build`.
- **Problem: Can't connect to ntfy server.**
  - **Solution**: Double-check the server URL and your internet connection.
- **Problem: Syncing isn't working.**
  - **Solution**: Verify your topic names and authentication settings. Check the Logs tab for any error messages.

## Development

- **Run tests**:
  ```bash
  make test
  ```
- **Format code** (requires `swiftformat`):
  ```bash
  make format
  ```
- **Lint code** (requires `swiftlint`):
  ```bash
  make lint
  ```
