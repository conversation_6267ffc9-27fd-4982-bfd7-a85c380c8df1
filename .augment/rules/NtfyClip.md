---
type: "always_apply"
---

项目名称：NtfyBoard for macOS

使用前调用 Context7 MCP 去阅读文档, 使用最新的技术, 最新的 UI 设计

1. 项目概述 (Project Overview)
NtfyBoard for macOS 是一款原生的 macOS 菜单栏工具，旨在无缝连接用户的剪贴板与 ntfy 服务。它能够实时监测剪贴板内容的变化，并将其同步到指定的 ntfy 主题 (Topic)；同时，它也能订阅一个 ntfy 主题，并将接收到的消息内容自动更新到本地剪贴板。

本项目将完全使用 Swift 和最新的 Apple 技术栈构建，采用以视图模型为核心的现代化架构，确保提供原生性能、高效率、可维护性以及符合苹果设计理念的用户体验。

2. 核心目标 (Core Objectives)
原生体验: 使用 Swift 和 SwiftUI 构建，提供一个轻量、高效、响应迅速的菜单栏应用。

双向同步: 实现剪贴板到 ntfy (发送) 和 ntfy 到剪贴板 (接收) 的双向数据流。

多内容支持: 可靠地同步纯文本和图片内容。

后台运行: 应用主要作为后台服务运行，通过菜单栏图标提供状态显示和控制入口。

现代架构: 采用视图模型 (ViewModel) 集中管理状态和逻辑，角色驱动 (Actor-based) 的后台任务确保并发安全，以及服务层 (Service Layer) 分离具体实现。

健壮稳定: 实现优雅的错误处理和自动重连机制，并内置循环发送保护逻辑。

3. 技术规格 (Technical Specifications)
开发语言: Swift 5.7+ 越新越好

用户界面:

主应用: SwiftUI，使用 `NavigationSplitView` 作为主窗口，并使用 `MenuBarExtra` 作为应用的菜单栏入口。

设置界面: SwiftUI，在主窗口中以标签页的形式提供详细的配置选项。

核心技术:

状态管理: Combine / ObservableObject (AppViewModel) 作为UI状态的唯一来源 (Single Source of Truth)。

并发模型: Swift Concurrency (async/await)，后台任务将使用 Actor 模型确保线程安全。

剪贴板交互: AppKit (NSPasteboard)。

网络通信: URLSession (async 方法和 URLSessionWebSocketTask)。

日志系统: OSLog (Logger)。

目标平台: macOS 13.0 (Ventura) 或更高版本。

依赖管理: Swift Package Manager (SPM)。

4. 架构设计 (System Architecture)
应用将遵循一个三层架构：视图层 (View)、视图模型层 (ViewModel) 和 服务/角色层 (Services & Actors)。

4.1. 视图层 (View Layer)
组件: `MainWindowView` (包含 `NavigationSplitView`), `StatusDetailView`, `SettingsDetailView`, `LogsDetailView`, `MenuBarView`。

职责:

完全被动地展示由 AppViewModel 提供的数据和状态。

将用户的操作（如点击按钮、修改配置）转发给 AppViewModel 进行处理。

严禁直接与服务层或角色层进行任何交互。

4.2. 视图模型层 (ViewModel Layer)
核心组件: AppViewModel: ObservableObject

职责:

应用的“大脑”: 作为整个应用状态和逻辑的中心协调者。

状态管理: 使用 @Published 属性暴露应用状态（如 isConnected, isSyncEnabled, errorMessage）给视图层。

生命周期管理: 负责初始化、启动、暂停和销毁后台任务角色 (ClipboardSender, ClipboardReceiver)。

循环保护: 持有上次接收内容的唯一标识符 (Content Identifier)，用于决策是否发送剪贴板的新内容，这是防止无限同步循环的核心机制。

配置协调: 作为 ConfigurationManager 的主要使用者，响应配置变更并将其传递给相关组件。

4.3. 服务与角色层 (Services & Actors)
此层包含执行具体工作的独立组件。

ClipboardSender: actor

职责: 监控并发送剪贴板内容。

逻辑: 在一个独立的 async 任务中运行。通过 ClipboardManager 监测 changeCount。当检测到变化时，读取内容并计算其唯一标识符，然后向 AppViewModel 查询是否应发送。获得许可后，调用 NtfyService 执行发送。

ClipboardReceiver: actor

职责: 连接 ntfy 并接收消息。

逻辑: 在一个独立的 async 任务中运行。通过 NtfyService 维持 WebSocket 连接，并内置重连逻辑。收到消息后，处理附件，计算内容的唯一标识符，然后通知 AppViewModel 更新状态，最后调用 ClipboardManager 更新剪贴板。

这些是无状态的 struct，提供单一、可复用的功能。

NtfyService

职责: 封装所有与 ntfy 服务器的 HTTP 和 WebSocket 通信。提供如 post(data:to:) 和 connect(to:) 等 async 方法。

ClipboardManager

职责: 封装所有与 NSPasteboard 的交互。提供如 getText(), setImage() 和 getChangeCount() 等方法。所有操作必须在 @MainActor 上下文中执行。

ConfigurationManager

职责: 封装 UserDefaults 的读写操作，提供类型安全的接口来访问所有用户配置（如 URL、主题、启用状态等）。

5. 关键逻辑与数据流 (Key Logic & Data Flow)
循环发送保护 (Loop Prevention)
此机制是项目成功的关键，必须严格实现。

接收时: 当 ClipboardReceiver 接收到新内容并准备更新剪贴板时，它必须：
a. 为该内容生成一个唯一标识符（建议使用内容的 SHA256 哈希值）。
b. 调用 appViewModel.didReceieveContent(identifier: ...)。
c. 调用 clipboardManager.setContent(...)。

发送时: 当 ClipboardSender 检测到剪贴板变化时，它必须：
a. 读取新内容并生成其唯一标识符。
b. 调用 appViewModel.shouldSendContent(identifier: ...) 进行查询。
c. AppViewModel 将此标识符与内部存储的“上次接收标识符”进行比较。如果匹配，返回 false。
d. 只有在 shouldSendContent 返回 true 时，ClipboardSender 才能继续发送。

6. 错误处理与日志
错误处理:

后台角色 (Sender, Receiver) 负责处理各自的瞬时错误（如网络中断），并实现带有指数退避策略的自动重试。

当发生无法自动恢复的错误（如无效配置、认证失败）时，角色应通知 AppViewModel。

AppViewModel 负责将错误状态发布给UI，以便用户可以看到清晰的提示信息。

日志记录:

全面使用 Apple 的统一日志系统 OSLog (Logger)。

使用一个单独的 `LogManager` 来管理和存储日志，这些日志可以在主窗口的“日志”选项卡中查看。